import React, { useState } from 'react';
import {
  File,
  Folder,
  FolderOpen,
  Play,
  Square,
  Save,
  Settings,
  Search,
  ChevronRight,
  ChevronDown
} from 'lucide-react';

// 文件樹結構
interface FileNode {
  name: string;
  type: 'file' | 'folder';
  children?: FileNode[];
  isOpen?: boolean;
}

const initialFileTree: FileNode[] = [
  {
    name: '策略開發',
    type: 'folder',
    isOpen: true,
    children: [
      {
        name: '指標庫',
        type: 'folder',
        isOpen: true,
        children: [
          { name: 'MA.py', type: 'file' },
          { name: 'RSI.py', type: 'file' },
          { name: 'MACD.py', type: 'file' },
          { name: 'Bollinger.py', type: 'file' }
        ]
      },
      {
        name: '選股策略',
        type: 'folder',
        isOpen: false,
        children: [
          { name: 'momentum_strategy.py', type: 'file' },
          { name: 'mean_reversion.py', type: 'file' }
        ]
      },
      {
        name: '交易策略',
        type: 'folder',
        isOpen: false,
        children: [
          { name: 'trend_following.py', type: 'file' },
          { name: 'grid_trading.py', type: 'file' }
        ]
      }
    ]
  }
];

// 程式碼內容
const codeContent = `# 均線突破策略
# 作者: CHIPO Auto Trading
# 版本: 1.0

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple

class MovingAverageStrategy:
    """
    均線突破策略
    當短期均線突破長期均線時產生買入信號
    當短期均線跌破長期均線時產生賣出信號
    """
    
    def __init__(self, short_window: int = 5, long_window: int = 20):
        self.short_window = short_window
        self.long_window = long_window
        self.positions = {}
        
    def calculate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        計算交易信號
        """
        # 計算移動平均線
        data['MA_short'] = data['close'].rolling(window=self.short_window).mean()
        data['MA_long'] = data['close'].rolling(window=self.long_window).mean()
        
        # 計算信號
        data['signal'] = 0
        data['signal'][self.short_window:] = np.where(
            data['MA_short'][self.short_window:] > data['MA_long'][self.short_window:], 1, 0
        )
        
        # 計算持倉變化
        data['positions'] = data['signal'].diff()
        
        return data
        
    def backtest(self, data: pd.DataFrame) -> Dict:
        """
        回測策略績效
        """
        signals_data = self.calculate_signals(data)
        
        # 計算收益率
        signals_data['returns'] = signals_data['close'].pct_change()
        signals_data['strategy_returns'] = signals_data['signal'].shift(1) * signals_data['returns']
        
        # 計算累積收益
        signals_data['cumulative_returns'] = (1 + signals_data['strategy_returns']).cumprod()
        
        # 計算績效指標
        total_return = signals_data['cumulative_returns'].iloc[-1] - 1
        sharpe_ratio = signals_data['strategy_returns'].mean() / signals_data['strategy_returns'].std() * np.sqrt(252)
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'data': signals_data
        }

# 使用範例
if __name__ == "__main__":
    # 初始化策略
    strategy = MovingAverageStrategy(short_window=5, long_window=20)
    
    # 載入數據 (這裡需要實際的股價數據)
    # data = load_stock_data("2330")  # 台積電
    
    # 執行回測
    # results = strategy.backtest(data)
    # print(f"總收益率: {results['total_return']:.2%}")
    # print(f"夏普比率: {results['sharpe_ratio']:.2f}")
`;

const StrategyDevelopmentPage: React.FC = () => {
  const [fileTree, setFileTree] = useState<FileNode[]>(initialFileTree);
  const [selectedFile, setSelectedFile] = useState<string>('MA.py');
  const [isRunning, setIsRunning] = useState(false);

  // 切換文件夾開關狀態
  const toggleFolder = (path: string[]) => {
    const updateTree = (nodes: FileNode[], currentPath: string[]): FileNode[] => {
      return nodes.map(node => {
        if (currentPath.length === 1 && node.name === currentPath[0]) {
          return { ...node, isOpen: !node.isOpen };
        } else if (currentPath.length > 1 && node.name === currentPath[0] && node.children) {
          return {
            ...node,
            children: updateTree(node.children, currentPath.slice(1))
          };
        }
        return node;
      });
    };
    
    setFileTree(updateTree(fileTree, path));
  };

  // 渲染文件樹
  const renderFileTree = (nodes: FileNode[], path: string[] = []): React.ReactNode => {
    return nodes.map((node, index) => {
      const currentPath = [...path, node.name];
      const isSelected = selectedFile === node.name && node.type === 'file';
      
      return (
        <div key={index} className="select-none">
          <div
            className={`flex items-center px-2 py-1 hover:bg-gray-700 cursor-pointer text-sm ${
              isSelected ? 'bg-blue-600 text-white' : 'text-gray-300'
            }`}
            style={{ paddingLeft: `${path.length * 16 + 8}px` }}
            onClick={() => {
              if (node.type === 'folder') {
                toggleFolder(currentPath);
              } else {
                setSelectedFile(node.name);
              }
            }}
          >
            {node.type === 'folder' ? (
              <>
                {node.isOpen ? (
                  <ChevronDown className="w-4 h-4 mr-1" />
                ) : (
                  <ChevronRight className="w-4 h-4 mr-1" />
                )}
                {node.isOpen ? (
                  <FolderOpen className="w-4 h-4 mr-2 text-yellow-400" />
                ) : (
                  <Folder className="w-4 h-4 mr-2 text-yellow-400" />
                )}
              </>
            ) : (
              <File className="w-4 h-4 mr-2 ml-5 text-blue-400" />
            )}
            <span>{node.name}</span>
          </div>
          {node.type === 'folder' && node.isOpen && node.children && (
            <div>
              {renderFileTree(node.children, currentPath)}
            </div>
          )}
        </div>
      );
    });
  };

  return (
    <div className="h-full bg-gray-900 flex">
      {/* 左側文件樹 */}
      <div className="w-1/4 h-full bg-gray-800 border-r border-gray-700">
        {/* 文件樹標題 */}
        <div className="bg-gray-700 px-3 py-2 border-b border-gray-600 flex items-center justify-between">
          <span className="text-white text-sm font-medium">檔案總管</span>
          <Search className="w-4 h-4 text-gray-400" />
        </div>

        {/* 文件樹內容 */}
        <div className="overflow-auto h-full">
          {renderFileTree(fileTree)}
        </div>
      </div>

      {/* 右側編輯器區域 */}
      <div className="flex-1 h-full flex flex-col bg-gray-900">
        {/* 編輯器標題欄 */}
        <div className="bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-white font-medium">{selectedFile}</span>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsRunning(!isRunning)}
                className={`flex items-center space-x-1 px-3 py-1 rounded text-sm ${
                  isRunning
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-green-600 hover:bg-green-700 text-white'
                }`}
              >
                {isRunning ? (
                  <>
                    <Square className="w-4 h-4" />
                    <span>停止</span>
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4" />
                    <span>執行</span>
                  </>
                )}
              </button>
              <button className="flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm text-white">
                <Save className="w-4 h-4" />
                <span>儲存</span>
              </button>
            </div>
          </div>
          <Settings className="w-5 h-5 text-gray-400 hover:text-white cursor-pointer" />
        </div>

        {/* 程式碼編輯器 */}
        <div className="flex-1 relative">
          <div className="absolute inset-0 flex">
            {/* 行號 */}
            <div className="bg-gray-800 text-gray-500 text-sm font-mono px-2 py-4 border-r border-gray-700 select-none">
              {codeContent.split('\n').map((_, index) => (
                <div key={index} className="leading-6 text-right">
                  {index + 1}
                </div>
              ))}
            </div>

            {/* 程式碼內容 */}
            <div className="flex-1 bg-gray-900 text-gray-100 text-sm font-mono p-4 overflow-auto">
              <pre className="leading-6 whitespace-pre-wrap">
                <code className="language-python">{codeContent}</code>
              </pre>
            </div>
          </div>
        </div>

        {/* 底部狀態欄 */}
        <div className="bg-gray-800 border-t border-gray-700 px-4 py-2 flex items-center justify-between text-sm text-gray-400">
          <div className="flex items-center space-x-4">
            <span>Python</span>
            <span>UTF-8</span>
            <span>LF</span>
          </div>
          <div className="flex items-center space-x-4">
            <span>第 1 行，第 1 列</span>
            <span className={`px-2 py-1 rounded text-xs ${
              isRunning ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300'
            }`}>
              {isRunning ? '執行中' : '就緒'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StrategyDevelopmentPage;
