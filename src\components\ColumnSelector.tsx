import React, { useState, useEffect } from 'react';
import { X, Check } from 'lucide-react';

interface ColumnConfig {
  key: string;
  label: string;
  visible: boolean;
  required?: boolean;
}

interface ColumnSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  columns: ColumnConfig[];
  onColumnsChange: (columns: ColumnConfig[]) => void;
  title?: string;
}

const ColumnSelector: React.FC<ColumnSelectorProps> = ({
  isOpen,
  onClose,
  columns,
  onColumnsChange,
  title = "欄位設定"
}) => {
  const [localColumns, setLocalColumns] = useState<ColumnConfig[]>(columns);

  // 當 columns prop 變更時同步更新本地狀態
  useEffect(() => {
    setLocalColumns(columns);
  }, [columns]);

  // 處理欄位勾選變更
  const handleColumnToggle = (key: string) => {
    setLocalColumns(prev => 
      prev.map(col => 
        col.key === key && !col.required 
          ? { ...col, visible: !col.visible }
          : col
      )
    );
  };

  // 確認變更
  const handleConfirm = () => {
    onColumnsChange(localColumns);
    onClose();
  };

  // 取消變更
  const handleCancel = () => {
    setLocalColumns(columns); // 重置為原始狀態
    onClose();
  };

  // 全選/全不選
  const handleSelectAll = () => {
    const allVisible = localColumns.every(col => col.visible || col.required);
    setLocalColumns(prev => 
      prev.map(col => ({ 
        ...col, 
        visible: col.required ? true : !allVisible 
      }))
    );
  };

  // 阻止點擊事件冒泡
  const handleModalClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center"
        onClick={handleCancel}
      >
        {/* 彈出視窗 */}
        <div 
          className="bg-gray-800 border border-gray-600 rounded-lg shadow-2xl w-80 max-h-96 flex flex-col"
          onClick={handleModalClick}
        >
          {/* 標題列 */}
          <div className="flex items-center justify-between px-4 py-3 border-b border-gray-600">
            <h3 className="text-white font-semibold text-sm">{title}</h3>
            <button
              onClick={handleCancel}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          {/* 操作按鈕 */}
          <div className="px-4 py-2 border-b border-gray-600">
            <button
              onClick={handleSelectAll}
              className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
            >
              {localColumns.every(col => col.visible || col.required) ? '全部取消' : '全部選取'}
            </button>
          </div>

          {/* 欄位列表 */}
          <div className="flex-1 overflow-y-auto p-2 max-h-48">
            <div className="space-y-1">
              {localColumns.map((column) => (
                <label
                  key={column.key}
                  className={`flex items-center space-x-3 p-2 rounded cursor-pointer transition-colors ${
                    column.required 
                      ? 'bg-gray-700 cursor-not-allowed' 
                      : 'hover:bg-gray-700'
                  }`}
                >
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={column.visible}
                      onChange={() => handleColumnToggle(column.key)}
                      disabled={column.required}
                      className="sr-only"
                    />
                    <div className={`w-4 h-4 border rounded flex items-center justify-center transition-colors ${
                      column.visible 
                        ? 'bg-blue-600 border-blue-600' 
                        : 'border-gray-500 bg-gray-800'
                    } ${
                      column.required 
                        ? 'opacity-50' 
                        : 'hover:border-blue-500'
                    }`}>
                      {column.visible && (
                        <Check className="w-3 h-3 text-white" />
                      )}
                    </div>
                  </div>
                  <span className={`text-sm flex-1 ${
                    column.visible ? 'text-white' : 'text-gray-400'
                  } ${
                    column.required ? 'opacity-75' : ''
                  }`}>
                    {column.label}
                    {column.required && (
                      <span className="text-xs text-gray-500 ml-1">(必要)</span>
                    )}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* 底部按鈕 */}
          <div className="flex items-center justify-end space-x-2 px-4 py-3 border-t border-gray-600">
            <button
              onClick={handleCancel}
              className="px-3 py-1.5 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleConfirm}
              className="px-3 py-1.5 text-sm text-white bg-blue-600 hover:bg-blue-700 rounded transition-colors"
            >
              確定
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default ColumnSelector;