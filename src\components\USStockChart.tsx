import React from 'react';

const USStockChart = () => {
  // 模擬美股數據
  const generateChartData = () => {
    const data = [];
    let price = 425.67;
    for (let i = 0; i < 100; i++) {
      price += (Math.random() - 0.5) * 5;
      data.push(price);
    }
    return data;
  };

  const chartData = generateChartData();
  const currentPrice = 425.67;
  const change = 8.23;
  const changePercent = 1.97;

  return (
    <div className="h-full bg-black rounded border border-gray-700 p-4">
      {/* Chart Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <span className="text-white font-bold">NASDAQ Composite</span>
          <span className="text-white">4:00:00 PM EST</span>
          <span className="text-green-400 font-bold text-lg">${currentPrice}</span>
          <span className="text-green-400">▲{change} +{changePercent}%</span>
          <span className="text-gray-400">Vol: 2.8B</span>
        </div>
      </div>

      {/* Chart Area */}
      <div className="relative h-64 mb-4">
        <svg className="w-full h-full">
          {/* Grid Lines */}
          {[0, 1, 2, 3, 4].map(i => (
            <line
              key={i}
              x1="0"
              y1={i * 64}
              x2="100%"
              y2={i * 64}
              stroke="#374151"
              strokeWidth="1"
            />
          ))}
          
          {/* Price Line */}
          <polyline
            fill="none"
            stroke="#10B981"
            strokeWidth="2"
            points={chartData.map((price, index) => 
              `${(index / chartData.length) * 100}%,${((430 - price) / 10) * 100}%`
            ).join(' ')}
          />
        </svg>
        
        {/* Price Labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-green-400 text-xs">
          <span>430.0</span>
          <span>427.5</span>
          <span>425.0</span>
          <span>422.5</span>
          <span>420.0</span>
          <span>417.5</span>
          <span>415.0</span>
        </div>
      </div>

      {/* Volume Chart */}
      <div className="h-20 relative">
        <div className="flex items-end h-full space-x-1">
          {Array.from({length: 50}, (_, i) => (
            <div
              key={i}
              className="bg-blue-500 flex-1"
              style={{height: `${Math.random() * 100}%`}}
            />
          ))}
        </div>
        <div className="absolute left-0 top-0 text-white text-xs">
          <div>3.0B</div>
          <div className="mt-2">1.5B</div>
          <div className="mt-2">0</div>
        </div>
      </div>

      {/* Time Labels */}
      <div className="flex justify-between text-gray-400 text-xs mt-2">
        <span>9:30</span>
        <span>11:00</span>
        <span>12:30</span>
        <span>2:00</span>
        <span>3:30</span>
        <span>4:00</span>
      </div>
    </div>
  );
};

export default USStockChart;