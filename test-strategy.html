<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>測試策略設定功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #2d2d2d;
            border-radius: 8px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #3d3d3d;
            border-radius: 6px;
        }
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #1a1a1a;
            border-radius: 4px;
            font-family: monospace;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #555;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 CHIPO Auto Trading - 策略功能測試</h1>
        
        <div class="test-section">
            <h2>📊 服務器狀態檢查</h2>
            <button class="test-button" onclick="checkServer()">檢查 Next.js 服務器</button>
            <div id="server-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>🎯 功能測試指南</h2>
            <p>請按照以下步驟測試策略設定和執行狀態功能：</p>
            <ol>
                <li>在下方的 iframe 中，點擊頂部工具列的「實盤監測」按鈕</li>
                <li>確認下拉選單出現，包含「策略設定」和「執行狀態」選項</li>
                <li>點擊「策略設定」，檢查是否正確跳轉到策略設定頁面</li>
                <li>返回後點擊「執行狀態」，檢查是否正確跳轉到執行狀態頁面</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>🖥️ Next.js 應用程式預覽</h2>
            <iframe src="http://localhost:3002/" title="CHIPO Auto Trading Next.js版本"></iframe>
        </div>
        
        <div class="test-section">
            <h2>🔧 故障排除</h2>
            <button class="test-button" onclick="openDevTools()">開啟開發者工具指南</button>
            <button class="test-button" onclick="checkConsole()">檢查控制台錯誤</button>
            <div id="troubleshoot-result" class="result"></div>
        </div>
    </div>

    <script>
        async function checkServer() {
            const resultDiv = document.getElementById('server-result');
            resultDiv.innerHTML = '正在檢查服務器...';
            
            try {
                const response = await fetch('http://localhost:3002/');
                if (response.ok) {
                    resultDiv.innerHTML = `
                        ✅ Next.js 服務器正常運行
                        狀態碼: ${response.status}
                        URL: http://localhost:3002/
                        時間: ${new Date().toLocaleTimeString()}
                    `;
                } else {
                    resultDiv.innerHTML = `❌ 服務器響應異常: ${response.status}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ 無法連接到服務器: ${error.message}`;
            }
        }
        
        function openDevTools() {
            const resultDiv = document.getElementById('troubleshoot-result');
            resultDiv.innerHTML = `
                📋 開發者工具使用指南：
                1. 按 F12 或右鍵選擇「檢查」開啟開發者工具
                2. 切換到「Console」標籤
                3. 在應用程式中點擊「實盤監測」按鈕
                4. 查看是否有 JavaScript 錯誤或警告
                5. 檢查網路請求是否正常
            `;
        }
        
        function checkConsole() {
            const resultDiv = document.getElementById('troubleshoot-result');
            resultDiv.innerHTML = `
                🔍 常見問題檢查：
                • 確認 Next.js 服務器在 http://localhost:3002/ 運行
                • 檢查瀏覽器控制台是否有 JavaScript 錯誤
                • 確認組件導入路徑正確
                • 檢查 TopToolbar 組件的事件處理是否正常
                • 驗證路由配置是否正確
            `;
        }
        
        // 自動檢查服務器狀態
        window.onload = function() {
            checkServer();
        };
    </script>
</body>
</html>
