import React, { useState, useRef, useCallback } from 'react';
import USStockChart from './USStockChart';
import USMarketData from './USMarketData';
import USStockList from './USStockList';
import USTechnicalAnalysis from './USTechnicalAnalysis';
import { usePanelContext } from '../contexts/PanelContext';

const USStockPage = () => {
  const { panels, updatePanel, getPanelById } = usePanelContext();
  
  // 初始面板大小 (百分比)
  const [panelSizes, setPanelSizes] = useState({
    topLeft: { width: 60, height: 60 },
    topRight: { width: 40, height: 60 },
    bottomLeft: { width: 60, height: 40 },
    bottomRight: { width: 40, height: 40 }
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<string | null>(null);

  const handleMouseDown = useCallback((divider: string) => {
    setIsDragging(divider);
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    setPanelSizes(prev => {
      const newSizes = { ...prev };
      
      if (isDragging === 'vertical') {
        const newLeftWidth = Math.max(20, Math.min(80, x));
        const newRightWidth = 100 - newLeftWidth;
        
        newSizes.topLeft.width = newLeftWidth;
        newSizes.bottomLeft.width = newLeftWidth;
        newSizes.topRight.width = newRightWidth;
        newSizes.bottomRight.width = newRightWidth;

        // 同步更新面板配置
        updatePanel('us-top-left', { size: { width: newLeftWidth, height: newSizes.topLeft.height } });
        updatePanel('us-top-right', { size: { width: newRightWidth, height: newSizes.topRight.height } });
        updatePanel('us-bottom-left', { size: { width: newLeftWidth, height: newSizes.bottomLeft.height } });
        updatePanel('us-bottom-right', { size: { width: newRightWidth, height: newSizes.bottomRight.height } });
      } else if (isDragging === 'horizontal') {
        const newTopHeight = Math.max(20, Math.min(80, y));
        const newBottomHeight = 100 - newTopHeight;
        
        newSizes.topLeft.height = newTopHeight;
        newSizes.topRight.height = newTopHeight;
        newSizes.bottomLeft.height = newBottomHeight;
        newSizes.bottomRight.height = newBottomHeight;

        // 同步更新面板配置
        updatePanel('us-top-left', { size: { width: newSizes.topLeft.width, height: newTopHeight } });
        updatePanel('us-top-right', { size: { width: newSizes.topRight.width, height: newTopHeight } });
        updatePanel('us-bottom-left', { size: { width: newSizes.bottomLeft.width, height: newBottomHeight } });
        updatePanel('us-bottom-right', { size: { width: newSizes.bottomRight.width, height: newBottomHeight } });
      }
      
      return newSizes;
    });
  }, [isDragging, updatePanel]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(null);
  }, []);

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = isDragging === 'vertical' ? 'col-resize' : 'row-resize';
      document.body.style.userSelect = 'none';
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 獲取面板配置
  const mainChartPanel = getPanelById('us-top-left');
  const marketDataPanel = getPanelById('us-top-right');
  const sectorListPanel = getPanelById('us-bottom-left');
  const technicalAnalysisPanel = getPanelById('us-bottom-right');

  return (
    <div 
      ref={containerRef}
      className="flex-1 relative bg-gray-900 overflow-hidden"
      style={{ minHeight: '400px' }}
      data-container-id="us-trading-area"
    >
      {/* 左上角面板 - 美股主圖表 */}
      <div 
        className="absolute bg-gray-900 border-r border-b border-gray-700"
        style={{
          left: 0,
          top: 0,
          width: `${panelSizes.topLeft.width}%`,
          height: `${panelSizes.topLeft.height}%`
        }}
        data-panel-id="us-top-left"
        data-panel-title={mainChartPanel?.title}
        data-panel-component={mainChartPanel?.component}
      >

        <div className="h-full p-2">
          <USStockChart />
        </div>
      </div>

      {/* 右上角面板 - 美股市場數據 */}
      <div 
        className="absolute bg-gray-900 border-b border-gray-700"
        style={{
          right: 0,
          top: 0,
          width: `${panelSizes.topRight.width}%`,
          height: `${panelSizes.topRight.height}%`
        }}
        data-panel-id="us-top-right"
        data-panel-title={marketDataPanel?.title}
        data-panel-component={marketDataPanel?.component}
      >

        <div className="h-full">
          <USMarketData />
        </div>
      </div>

      {/* 左下角面板 - 美股股票列表 */}
      <div 
        className="absolute bg-gray-900 border-r border-gray-700"
        style={{
          left: 0,
          bottom: 0,
          width: `${panelSizes.bottomLeft.width}%`,
          height: `${panelSizes.bottomLeft.height}%`
        }}
        data-panel-id="us-bottom-left"
        data-panel-title={sectorListPanel?.title}
        data-panel-component={sectorListPanel?.component}
      >

        <div className="h-full">
          <USStockList />
        </div>
      </div>

      {/* 右下角面板 - 美股技術分析 */}
      <div 
        className="absolute bg-gray-900"
        style={{
          right: 0,
          bottom: 0,
          width: `${panelSizes.bottomRight.width}%`,
          height: `${panelSizes.bottomRight.height}%`
        }}
        data-panel-id="us-bottom-right"
        data-panel-title={technicalAnalysisPanel?.title}
        data-panel-component={technicalAnalysisPanel?.component}
      >

        <div className="h-full">
          <USTechnicalAnalysis />
        </div>
      </div>

      {/* 垂直分割線 */}
      <div
        className="absolute top-0 bottom-0 w-1 bg-gray-600 hover:bg-blue-500 cursor-col-resize z-10 transition-colors"
        style={{
          left: `${panelSizes.topLeft.width}%`,
          transform: 'translateX(-50%)'
        }}
        onMouseDown={() => handleMouseDown('vertical')}
        data-divider-id="vertical-divider"
        data-divider-type="vertical"
      >
        <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-3 hover:bg-blue-500/20" />
      </div>

      {/* 水平分割線 */}
      <div
        className="absolute left-0 right-0 h-1 bg-gray-600 hover:bg-blue-500 cursor-row-resize z-10 transition-colors"
        style={{
          top: `${panelSizes.topLeft.height}%`,
          transform: 'translateY(-50%)'
        }}
        onMouseDown={() => handleMouseDown('horizontal')}
        data-divider-id="horizontal-divider"
        data-divider-type="horizontal"
      >
        <div className="absolute inset-x-0 top-1/2 transform -translate-y-1/2 h-3 hover:bg-blue-500/20" />
      </div>

      {/* 拖拽時的覆蓋層 */}
      {isDragging && (
        <div className="absolute inset-0 z-20 bg-transparent" data-overlay-active="true" />
      )}
    </div>
  );
};

export default USStockPage;