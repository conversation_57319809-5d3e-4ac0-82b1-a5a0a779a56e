import React from 'react';

const USMarketData = () => {
  const marketData = [
    { code: 'AAPL', name: 'Apple Inc.', price: '$189.25', change: '▲3.45', changePercent: '+1.86', volume: '45.2M', marketCap: '2.95T', time: '4:00:00 PM' },
    { code: 'MSFT', name: 'Microsoft', price: '$378.91', change: '▲5.67', changePercent: '+1.52', volume: '28.7M', marketCap: '2.81T', time: '4:00:00 PM' },
    { code: 'GOOGL', name: 'Alphabet', price: '$142.56', change: '▼2.34', changePercent: '-1.62', volume: '32.1M', marketCap: '1.78T', time: '4:00:00 PM' },
    { code: 'AMZN', name: 'Amazon', price: '$151.94', change: '▲1.89', changePercent: '+1.26', volume: '41.8M', marketCap: '1.57T', time: '4:00:00 PM' },
    { code: 'TSLA', name: '<PERSON><PERSON>', price: '$248.42', change: '▲12.67', changePercent: '+5.38', volume: '89.3M', marketCap: '789B', time: '4:00:00 PM' },
    { code: 'NVDA', name: 'NVIDIA', price: '$875.28', change: '▲18.45', changePercent: '+2.15', volume: '52.6M', marketCap: '2.16T', time: '4:00:00 PM' },
  ];

  const sectorData = [
    { time: '15:45:00', buy: '125.8M', sell: '98.2M', volume: '2.8B', buyVol: '1.2B', sellVol: '1.1B', netVol: '0.1B', buyCount: '45.2K', sellCount: '38.7K' },
    { time: '15:50:00', buy: '134.2M', sell: '102.5M', volume: '2.9B', buyVol: '1.3B', sellVol: '1.2B', netVol: '0.1B', buyCount: '48.1K', sellCount: '41.3K' },
    { time: '15:55:00', buy: '118.7M', sell: '89.3M', volume: '2.7B', buyVol: '1.1B', sellVol: '1.0B', netVol: '0.1B', buyCount: '42.8K', sellCount: '35.9K' },
  ];

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* Top Chart Area */}
      <div className="h-64 p-2 border-b border-gray-700">
        <div className="bg-black rounded border border-gray-700 h-full p-2">
          <div className="flex items-center justify-between mb-2">
            <span className="text-white text-sm font-bold">S&P 500 Index</span>
            <span className="text-white text-sm">4:00:00 PM EST</span>
            <span className="text-green-400 font-bold">4,567.89</span>
            <span className="text-green-400">▲23.45 +0.52%</span>
            <span className="text-gray-400">$2.8T Vol</span>
          </div>
          
          {/* Mini Chart */}
          <div className="h-32 relative">
            <svg className="w-full h-full">
              <polyline
                fill="none"
                stroke="#10B981"
                strokeWidth="1"
                points="0,80 50,75 100,70 150,68 200,65 250,60 300,55"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Stock List */}
      <div className="flex-1 overflow-auto custom-scrollbar">
        <table className="w-full text-xs">
          <thead className="bg-gray-800 sticky top-0">
            <tr className="text-gray-300">
              <th className="px-1 py-1 text-left">Symbol</th>
              <th className="px-1 py-1 text-left">Name</th>
              <th className="px-1 py-1 text-right">Price</th>
              <th className="px-1 py-1 text-right">Change</th>
              <th className="px-1 py-1 text-right">%</th>
              <th className="px-1 py-1 text-right">Volume</th>
              <th className="px-1 py-1 text-right">Market Cap</th>
              <th className="px-1 py-1 text-center">Time</th>
            </tr>
          </thead>
          <tbody>
            {marketData.map((stock, index) => (
              <tr key={index} className="hover:bg-gray-800 border-b border-gray-800">
                <td className="px-1 py-1 text-white text-xs font-semibold">{stock.code}</td>
                <td className="px-1 py-1 text-white text-xs">{stock.name}</td>
                <td className={`px-1 py-1 text-right text-xs ${
                  stock.change.includes('▲') ? 'text-green-400' : 
                  stock.change.includes('▼') ? 'text-red-400' : 'text-white'
                }`}>{stock.price}</td>
                <td className={`px-1 py-1 text-right text-xs ${
                  stock.change.includes('▲') ? 'text-green-400' : 
                  stock.change.includes('▼') ? 'text-red-400' : 'text-white'
                }`}>{stock.change}</td>
                <td className={`px-1 py-1 text-right text-xs ${
                  stock.changePercent.includes('+') ? 'text-green-400' : 
                  stock.changePercent.includes('-') ? 'text-red-400' : 'text-white'
                }`}>{stock.changePercent}</td>
                <td className="px-1 py-1 text-white text-right text-xs">{stock.volume}</td>
                <td className="px-1 py-1 text-white text-right text-xs">{stock.marketCap}</td>
                <td className="px-1 py-1 text-gray-300 text-center text-xs">{stock.time}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Bottom Time Data */}
      <div className="h-32 border-t border-gray-700 overflow-auto custom-scrollbar">
        <table className="w-full text-xs">
          <thead className="bg-gray-800 sticky top-0">
            <tr className="text-gray-300">
              <th className="px-1 py-1 text-center">Time</th>
              <th className="px-1 py-1 text-right">Buy Orders</th>
              <th className="px-1 py-1 text-right">Sell Orders</th>
              <th className="px-1 py-1 text-right">Volume</th>
              <th className="px-1 py-1 text-right">Buy Vol</th>
              <th className="px-1 py-1 text-right">Sell Vol</th>
              <th className="px-1 py-1 text-right">Net Vol</th>
              <th className="px-1 py-1 text-right">Buy Count</th>
              <th className="px-1 py-1 text-right">Sell Count</th>
            </tr>
          </thead>
          <tbody>
            {sectorData.map((data, index) => (
              <tr key={index} className="hover:bg-gray-800 border-b border-gray-800">
                <td className="px-1 py-1 text-gray-300 text-center">{data.time}</td>
                <td className="px-1 py-1 text-green-400 text-right">{data.buy}</td>
                <td className="px-1 py-1 text-red-400 text-right">{data.sell}</td>
                <td className="px-1 py-1 text-white text-right">{data.volume}</td>
                <td className="px-1 py-1 text-white text-right">{data.buyVol}</td>
                <td className="px-1 py-1 text-white text-right">{data.sellVol}</td>
                <td className="px-1 py-1 text-white text-right">{data.netVol}</td>
                <td className="px-1 py-1 text-white text-right">{data.buyCount}</td>
                <td className="px-1 py-1 text-white text-right">{data.sellCount}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default USMarketData;