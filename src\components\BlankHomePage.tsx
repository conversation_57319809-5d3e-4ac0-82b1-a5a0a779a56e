import React from 'react';
import { Bar<PERSON><PERSON>2, <PERSON>, <PERSON>ting<PERSON>, ArrowRight } from 'lucide-react';

const BlankHomePage = () => {
  // 模擬市場數據
  const marketData = {
    taiwan: {
      name: '台灣加權指數',
      value: '21,803.72',
      change: '+192.68',
      changePercent: '+0.89%',
      status: 'up',
    },
    us: {
      name: '道瓊工業指數',
      value: '38,798.99',
      change: '-65.11',
      changePercent: '-0.17%',
      status: 'down',
    },
  };

  const FeatureCard = ({ icon, title, description, link, linkText }: {
    icon: React.ReactNode;
    title: string;
    description: string;
    link: string;
    linkText: string;
  }) => (
    <div className="bg-gray-800 rounded-lg p-6 flex flex-col justify-between transform hover:scale-105 transition-transform duration-300">
      <div>
        <div className="flex items-center space-x-4 mb-4">
          <div className="bg-gray-700 p-3 rounded-lg">{icon}</div>
          <h3 className="text-xl font-bold text-white">{title}</h3>
        </div>
        <p className="text-gray-400 mb-6">{description}</p>
      </div>
      <a href={link} className="text-blue-400 hover:text-blue-300 flex items-center space-x-2 font-semibold">
        <span>{linkText}</span>
        <ArrowRight className="w-4 h-4" />
      </a>
    </div>
  );

  return (
    <div className="flex-1 bg-gray-900 p-8 overflow-y-auto">
      <div className="max-w-7xl mx-auto">
        <header className="mb-12">
          <h1 className="text-4xl font-bold text-white mb-2">歡迎使用 CHIPO 自動交易</h1>
          <p className="text-lg text-gray-400">您的智慧化交易夥伴，從這裡開始探索市場。</p>
        </header>

        {/* 市場總覽 */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-white mb-6">市場總覽</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* 台股卡片 */}
            <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
              <h3 className="text-lg font-semibold text-gray-300 mb-4">{marketData.taiwan.name}</h3>
              <div className={`text-4xl font-bold ${marketData.taiwan.status === 'up' ? 'text-red-500' : 'text-green-500'}`}>
                {marketData.taiwan.value}
              </div>
              <div className={`flex items-center space-x-4 mt-2 ${marketData.taiwan.status === 'up' ? 'text-red-400' : 'text-green-400'}`}>
                <span>{marketData.taiwan.change}</span>
                <span>({marketData.taiwan.changePercent})</span>
              </div>
            </div>
            {/* 美股卡片 */}
            <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
              <h3 className="text-lg font-semibold text-gray-300 mb-4">{marketData.us.name}</h3>
              <div className={`text-4xl font-bold ${marketData.us.status === 'up' ? 'text-red-500' : 'text-green-500'}`}>
                {marketData.us.value}
              </div>
              <div className={`flex items-center space-x-4 mt-2 ${marketData.us.status === 'up' ? 'text-red-400' : 'text-green-400'}`}>
                <span>{marketData.us.change}</span>
                <span>({marketData.us.changePercent})</span>
              </div>
            </div>
          </div>
        </section>

        {/* 功能導航 */}
        <section>
          <h2 className="text-2xl font-semibold text-white mb-6">快速開始</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <FeatureCard
              icon={<BarChart2 className="w-8 h-8 text-cyan-400" />}
              title="大盤分析"
              description="深入了解台股與美股市場動態，掌握即時報價與技術線圖。"
              link="#" // 這裡可以根據路由設定修改
              linkText="前往大盤頁面"
            />
            <FeatureCard
              icon={<Star className="w-8 h-8 text-yellow-400" />}
              title="自選股"
              description="建立並管理您的個人化股票清單，輕鬆追蹤關注的標的。"
              link="#" // 這裡可以根據路由設定修改
              linkText="管理自選清單"
            />
            <FeatureCard
              icon={<Settings className="w-8 h-8 text-purple-400" />}
              title="策略設定"
              description="設計、回測並部署您的交易策略，實現自動化交易。"
              link="#" // 這裡可以根據路由設定修改
              linkText="設定交易策略"
            />
          </div>
        </section>
      </div>
    </div>
  );
};

export default BlankHomePage;