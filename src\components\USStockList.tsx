import React from 'react';

const USStockList = () => {
  const sectors = [
    { sector: 'Sector Name', time: 'Time', index: 'Index', change: 'Change', changePercent: 'Change%', volume: 'Volume', volumePercent: 'Vol%', avgPrice: 'Avg Price', comparePrice: 'Weight%' },
    { sector: 'Technology', time: '4:00', index: '2,847.32', change: '▲45.67', changePercent: '+1.63', volume: '892.5B', volumePercent: '35.2', avgPrice: '287.45', comparePrice: '+2.15' },
    { sector: 'Healthcare', time: '4:00', index: '1,523.89', change: '▲12.34', changePercent: '+0.82', volume: '234.7B', volumePercent: '9.3', avgPrice: '156.78', comparePrice: '+0.45' },
    { sector: 'Financial', time: '4:00', index: '1,789.45', change: '▼8.92', changePercent: '-0.49', volume: '445.2B', volumePercent: '17.6', avgPrice: '89.23', comparePrice: '-0.28' },
    { sector: 'Consumer Disc.', time: '4:00', index: '1,234.67', change: '▲23.45', changePercent: '+1.94', volume: '356.8B', volumePercent: '14.1', avgPrice: '145.32', comparePrice: '+1.12' },
    { sector: 'Communication', time: '4:00', index: '987.23', change: '▼5.67', changePercent: '-0.57', volume: '198.4B', volumePercent: '7.8', avgPrice: '78.91', comparePrice: '-0.33' },
    { sector: 'Industrials', time: '4:00', index: '1,456.78', change: '▲18.92', changePercent: '+1.32', volume: '287.6B', volumePercent: '11.4', avgPrice: '123.45', comparePrice: '+0.89' },
    { sector: 'Energy', time: '4:00', index: '678.45', change: '▲34.56', changePercent: '+5.37', volume: '156.3B', volumePercent: '6.2', avgPrice: '67.89', comparePrice: '+2.78' },
  ];

  return (
    <div className="h-full bg-gray-900 border-t border-gray-700">
      <div className="overflow-auto h-full custom-scrollbar">
        <table className="w-full text-xs">
          <thead className="bg-gray-800 sticky top-0">
            <tr className="text-gray-300">
              <th className="px-2 py-1 text-left border-r border-gray-700">Sector</th>
              <th className="px-2 py-1 text-center border-r border-gray-700">Time</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">Index</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">Change</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">Change%</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">Volume</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">Vol%</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">Avg Price</th>
              <th className="px-2 py-1 text-right">Weight%</th>
            </tr>
          </thead>
          <tbody>
            {sectors.slice(1).map((sector, index) => (
              <tr key={index} className="hover:bg-gray-800 border-b border-gray-800">
                <td className="px-2 py-1 text-white border-r border-gray-700">{sector.sector}</td>
                <td className="px-2 py-1 text-gray-300 text-center border-r border-gray-700">{sector.time}</td>
                <td className={`px-2 py-1 text-right border-r border-gray-700 ${
                  sector.change.includes('▲') ? 'text-green-400' : 
                  sector.change.includes('▼') ? 'text-red-400' : 'text-white'
                }`}>{sector.index}</td>
                <td className={`px-2 py-1 text-right border-r border-gray-700 ${
                  sector.change.includes('▲') ? 'text-green-400' : 
                  sector.change.includes('▼') ? 'text-red-400' : 'text-white'
                }`}>{sector.change}</td>
                <td className={`px-2 py-1 text-right border-r border-gray-700 ${
                  sector.changePercent.includes('+') ? 'text-green-400' : 
                  sector.changePercent.includes('-') ? 'text-red-400' : 'text-white'
                }`}>{sector.changePercent}</td>
                <td className="px-2 py-1 text-white text-right border-r border-gray-700">{sector.volume}</td>
                <td className="px-2 py-1 text-white text-right border-r border-gray-700">{sector.volumePercent}</td>
                <td className="px-2 py-1 text-white text-right border-r border-gray-700">{sector.avgPrice}</td>
                <td className={`px-2 py-1 text-right ${
                  sector.comparePrice.includes('+') ? 'text-green-400' : 
                  sector.comparePrice.includes('-') ? 'text-red-400' : 'text-white'
                }`}>{sector.comparePrice}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default USStockList;