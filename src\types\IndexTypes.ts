// 索引碼系統類型定義
export interface IndexConfig {
  // 主項編碼
  mainCode: string; // A, B, C, E
  mainName: string; // 大盤, 自選股, 庫存股, 策略開發
  
  // 子項編碼
  subCode?: string; // A1, A2, B1, B2, E1-E7
  subName?: string; // 台股, 美股, 策略分析, 指標開發等
  
  // 類股編碼 (僅適用於台股)
  categoryCode?: string; // 上市, 上櫃, 期貨
  categoryName?: string; // 上市類股, 上櫃類股, 股票期貨
  
  // 視窗編碼
  windowCode?: string; // A1-上市1, A1-上市2, A1-上櫃1, A1-期貨1
  windowIndex?: number; // 1, 2, 3, 4
}

export interface PanelIndexConfig {
  id: string;
  index: IndexConfig;
  displayCode: string; // 完整顯示編碼，如 "A1-上市1"
}

// 主項目定義
export const MAIN_CATEGORIES = {
  MARKET: { code: 'A', name: '大盤' },
  WATCHLIST: { code: 'B', name: '自選股' },
  PORTFOLIO: { code: 'C', name: '庫存股' },
  STRATEGY: { code: 'E', name: '策略開發' }
} as const;

// 大盤子項目
export const MARKET_SUB_CATEGORIES = {
  TAIWAN: { code: 'A1', name: '台股' },
  US: { code: 'A2', name: '美股' }
} as const;

// 台股類股分類
export const TAIWAN_STOCK_CATEGORIES = {
  LISTED: { code: '上市', name: '上市類股' },
  OTC: { code: '上櫃', name: '上櫃類股' },
  FUTURES: { code: '期貨', name: '股票期貨' }
} as const;

// 策略開發子項目
export const STRATEGY_SUB_CATEGORIES = {
  ANALYSIS: { code: 'E1', name: '策略分析' },
  INDICATOR: { code: 'E2', name: '指標開發' },
  STOCK_SELECTION: { code: 'E3', name: '選股策略' },
  ALERT: { code: 'E4', name: '警示策略' },
  TRADING: { code: 'E5', name: '交易策略' },
  BACKTEST: { code: 'E6', name: '回測' },
  PERFORMANCE: { code: 'E7', name: '績效分析' }
} as const;