'use client';

import React, { useState } from 'react';
import BlankHomePage from '@/components/BlankHomePage';
import TaiwanStockPageWithSplitPane from '@/components/TaiwanStockPageWithSplitPane';
import StrategySettingsPage from '@/components/StrategySettingsPage';
import ExecutionStatusPage from '@/components/ExecutionStatusPage';
import StrategyDevelopmentPage from '@/components/StrategyDevelopmentPage';
import SettingsPage from '@/components/SettingsPage';
import TopToolbar from '@/components/TopToolbar';
import { PanelProvider } from '@/contexts/PanelContext';

export default function App() {
  const [currentMarket, setCurrentMarket] = useState<'taiwan' | 'us'>('taiwan');
  const [currentPage, setCurrentPage] = useState<'home' | 'market' | 'watchlist' | 'strategy-settings' | 'execution-status' | 'strategy-development' | 'settings'>('market');
  const [showStrategyPanel, setShowStrategyPanel] = useState<boolean>(false);
  const [strategyCategory, setStrategyCategory] = useState<string>('strategySettings');

  const handleMarketChange = (market: 'taiwan' | 'us') => {
    setCurrentMarket(market);
  };

  const handlePageChange = (page: 'home' | 'market' | 'watchlist' | 'strategy-settings' | 'execution-status' | 'strategy-development' | 'settings') => {
    console.log('Page change requested:', page);
    setCurrentPage(page);
    // 當切換到其他頁面時，關閉策略面板
    if (page !== 'market') {
      setShowStrategyPanel(false);
    }
  };

  const handleToggleStrategyPanel = (category: string) => {
    setStrategyCategory(category);
    setShowStrategyPanel(true);
    // 確保在市場頁面
    if (currentPage !== 'market') {
      setCurrentPage('market');
    }
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'home':
        return <BlankHomePage />;
      case 'market':
        return (
          <TaiwanStockPageWithSplitPane
            showStrategyPanel={showStrategyPanel}
            strategyCategory={strategyCategory}
            onCloseStrategyPanel={() => setShowStrategyPanel(false)}
          />
        );
      case 'strategy-settings':
        return <StrategySettingsPage />;
      case 'execution-status':
        return <ExecutionStatusPage />;
      case 'strategy-development':
        return <StrategyDevelopmentPage />;
      case 'settings':
        return <SettingsPage />;
      default:
        return (
          <TaiwanStockPageWithSplitPane
            showStrategyPanel={showStrategyPanel}
            strategyCategory={strategyCategory}
            onCloseStrategyPanel={() => setShowStrategyPanel(false)}
          />
        );
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gray-900">
      <TopToolbar
        currentMarket={currentMarket}
        onMarketChange={handleMarketChange}
        currentPage={currentPage}
        onPageChange={handlePageChange}
        showStrategyPanel={showStrategyPanel}
        onToggleStrategyPanel={handleToggleStrategyPanel}
      />
      <PanelProvider market={currentMarket}>
        <div className="flex-1">
          {renderCurrentPage()}
        </div>
      </PanelProvider>
    </div>
  );
}
