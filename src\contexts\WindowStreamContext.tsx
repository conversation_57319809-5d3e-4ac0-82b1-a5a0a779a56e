import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from 'react';

interface WindowData {
  id: string;
  type: 'chart' | 'list' | 'market' | 'technical';
  data: any;
  lastUpdate: number;
  isLoading: boolean;
  error?: string;
}

interface StreamConfig {
  windowId: string;
  updateInterval: number;
  priority: 'high' | 'medium' | 'low';
  enabled: boolean;
}

interface WindowStreamContextType {
  windows: Record<string, WindowData>;
  streamConfigs: Record<string, StreamConfig>;
  updateWindow: (windowId: string, data: any) => void;
  setWindowLoading: (windowId: string, loading: boolean) => void;
  setWindowError: (windowId: string, error?: string) => void;
  configureStream: (windowId: string, config: Partial<StreamConfig>) => void;
  pauseStream: (windowId: string) => void;
  resumeStream: (windowId: string) => void;
  pauseAllStreams: () => void;
  resumeAllStreams: () => void;
}

const WindowStreamContext = createContext<WindowStreamContextType | undefined>(undefined);

export const useWindowStream = () => {
  const context = useContext(WindowStreamContext);
  if (!context) {
    throw new Error('useWindowStream must be used within a WindowStreamProvider');
  }
  return context;
};

interface WindowStreamProviderProps {
  children: React.ReactNode;
}

export const WindowStreamProvider: React.FC<WindowStreamProviderProps> = ({ children }) => {
  const [windows, setWindows] = useState<Record<string, WindowData>>({});
  const [streamConfigs, setStreamConfigs] = useState<Record<string, StreamConfig>>({});
  const intervalRefs = useRef<Record<string, NodeJS.Timeout>>({});
  const [globalPaused, setGlobalPaused] = useState(false);

  // 更新視窗數據
  const updateWindow = useCallback((windowId: string, data: any) => {
    setWindows(prev => ({
      ...prev,
      [windowId]: {
        ...prev[windowId],
        id: windowId,
        data,
        lastUpdate: Date.now(),
        isLoading: false,
        error: undefined
      }
    }));
  }, []);

  // 設置視窗載入狀態
  const setWindowLoading = useCallback((windowId: string, loading: boolean) => {
    setWindows(prev => ({
      ...prev,
      [windowId]: {
        ...prev[windowId],
        id: windowId,
        isLoading: loading
      }
    }));
  }, []);

  // 設置視窗錯誤狀態
  const setWindowError = useCallback((windowId: string, error?: string) => {
    setWindows(prev => ({
      ...prev,
      [windowId]: {
        ...prev[windowId],
        id: windowId,
        error,
        isLoading: false
      }
    }));
  }, []);

  // 配置數據流
  const configureStream = useCallback((windowId: string, config: Partial<StreamConfig>) => {
    setStreamConfigs(prev => ({
      ...prev,
      [windowId]: {
        ...prev[windowId],
        windowId,
        updateInterval: 1000, // 預設 1 秒
        priority: 'medium',
        enabled: true,
        ...config
      }
    }));
  }, []);

  // 暫停特定視窗的數據流
  const pauseStream = useCallback((windowId: string) => {
    if (intervalRefs.current[windowId]) {
      clearInterval(intervalRefs.current[windowId]);
      delete intervalRefs.current[windowId];
    }
    setStreamConfigs(prev => ({
      ...prev,
      [windowId]: {
        ...prev[windowId],
        enabled: false
      }
    }));
  }, []);

  // 恢復特定視窗的數據流
  const resumeStream = useCallback((windowId: string) => {
    setStreamConfigs(prev => ({
      ...prev,
      [windowId]: {
        ...prev[windowId],
        enabled: true
      }
    }));
  }, []);

  // 暫停所有數據流
  const pauseAllStreams = useCallback(() => {
    setGlobalPaused(true);
    Object.keys(intervalRefs.current).forEach(windowId => {
      if (intervalRefs.current[windowId]) {
        clearInterval(intervalRefs.current[windowId]);
        delete intervalRefs.current[windowId];
      }
    });
  }, []);

  // 恢復所有數據流
  const resumeAllStreams = useCallback(() => {
    setGlobalPaused(false);
  }, []);

  // 模擬數據更新函數
  const simulateDataUpdate = useCallback((windowId: string, type: string) => {
    setWindowLoading(windowId, true);
    
    // 模擬網路延遲
    setTimeout(() => {
      try {
        let mockData;
        
        switch (type) {
          case 'chart':
            mockData = {
              price: (Math.random() * 1000 + 100).toFixed(2),
              change: (Math.random() * 10 - 5).toFixed(2),
              volume: Math.floor(Math.random() * 1000000),
              timestamp: Date.now()
            };
            break;
          case 'list':
            mockData = {
              stocks: Array.from({ length: 10 }, (_, i) => ({
                code: `${1000 + i}`,
                name: `股票${i + 1}`,
                price: (Math.random() * 100 + 10).toFixed(2),
                change: (Math.random() * 5 - 2.5).toFixed(2)
              })),
              timestamp: Date.now()
            };
            break;
          case 'market':
            mockData = {
              index: (Math.random() * 20000 + 15000).toFixed(2),
              change: (Math.random() * 200 - 100).toFixed(2),
              volume: Math.floor(Math.random() * 10000000),
              timestamp: Date.now()
            };
            break;
          case 'technical':
            mockData = {
              rsi: (Math.random() * 100).toFixed(2),
              macd: (Math.random() * 10 - 5).toFixed(2),
              volume: Math.floor(Math.random() * 1000000),
              timestamp: Date.now()
            };
            break;
          default:
            mockData = { timestamp: Date.now() };
        }
        
        updateWindow(windowId, mockData);
      } catch (error) {
        setWindowError(windowId, error instanceof Error ? error.message : '數據更新失敗');
      }
    }, Math.random() * 500 + 100); // 100-600ms 隨機延遲
  }, [updateWindow, setWindowLoading, setWindowError]);

  // 管理數據流間隔
  useEffect(() => {
    if (globalPaused) return;

    Object.entries(streamConfigs).forEach(([windowId, config]) => {
      if (!config.enabled) return;

      // 清除現有間隔
      if (intervalRefs.current[windowId]) {
        clearInterval(intervalRefs.current[windowId]);
      }

      // 根據優先級調整更新頻率
      let actualInterval = config.updateInterval;
      switch (config.priority) {
        case 'high':
          actualInterval = Math.max(500, config.updateInterval * 0.5);
          break;
        case 'low':
          actualInterval = config.updateInterval * 2;
          break;
        default:
          actualInterval = config.updateInterval;
      }

      // 設置新的間隔
      intervalRefs.current[windowId] = setInterval(() => {
        const windowData = windows[windowId];
        if (windowData && !windowData.isLoading) {
          simulateDataUpdate(windowId, windowData.type);
        }
      }, actualInterval);
    });

    // 清理函數
    return () => {
      Object.values(intervalRefs.current).forEach(interval => {
        if (interval) clearInterval(interval);
      });
      intervalRefs.current = {};
    };
  }, [streamConfigs, globalPaused, simulateDataUpdate, windows]);

  const value: WindowStreamContextType = {
    windows,
    streamConfigs,
    updateWindow,
    setWindowLoading,
    setWindowError,
    configureStream,
    pauseStream,
    resumeStream,
    pauseAllStreams,
    resumeAllStreams
  };

  return (
    <WindowStreamContext.Provider value={value}>
      {children}
    </WindowStreamContext.Provider>
  );
};