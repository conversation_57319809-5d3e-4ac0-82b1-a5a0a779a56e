import { useState, useCallback, useEffect } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import StockList from './StockList';
import RightPanelContainer from './RightPanelContainer';
import TaiwanStockSidebar from './TaiwanStockSidebar';
import StrategySidebar from './StrategySidebar';
import StrategyContent from './StrategyContent';
import { usePanelContext } from '../contexts/PanelContext';

interface TaiwanStockPageWithSplitPaneProps {
  showStrategyPanel?: boolean;
  strategyCategory?: string;
  onCloseStrategyPanel?: () => void;
  lastSelectedStockCategory?: {code: string, name: string} | null;
  onLastSelectedStockCategoryChange?: (category: {code: string, name: string} | null) => void;
}

const TaiwanStockPageWithSplitPane: React.FC<TaiwanStockPageWithSplitPaneProps> = ({
  showStrategyPanel = false,
  strategyCategory = 'strategySettings',
  onCloseStrategyPanel,
  lastSelectedStockCategory,
  onLastSelectedStockCategoryChange
}) => {
  const { updateTaiwanCategoryPanel } = usePanelContext();
  
  const [selectedCategory, setSelectedCategory] = useState('listed');
  const [selectedStockCategory, setSelectedStockCategory] = useState<{code: string, name: string} | null>(null);
  const [selectedStock, setSelectedStock] = useState<{code: string, name: string} | null>(null);
  const [selectedStrategyCategory, setSelectedStrategyCategory] = useState<string>(strategyCategory);



  // 處理類股選擇變更
  const handleCategorySelect = useCallback((category: string) => {
    setSelectedCategory(category);
    setSelectedStockCategory(null);
    
    if (updateTaiwanCategoryPanel) {
      const categoryMap: Record<string, 'listed' | 'otc' | 'futures'> = {
        'listed': 'listed',
        'otc': 'otc',
        'futures': 'futures'
      };
      updateTaiwanCategoryPanel(categoryMap[category]);
    }
  }, [updateTaiwanCategoryPanel]);

  // 處理類股中個股的選擇
  const handleStockCategorySelect = useCallback((categoryCode: string, categoryName: string) => {
    console.log(`選中類股: ${categoryName} (${categoryCode})，將在下方顯示該類股的個股清單`);
    const selectedCategory = { code: categoryCode, name: categoryName };
    setSelectedStockCategory(selectedCategory);
    // 記錄最後一次被點選的類股到父組件狀態
    if (onLastSelectedStockCategoryChange) {
      onLastSelectedStockCategoryChange(selectedCategory);
    }
    setSelectedStock(null); // 清除之前選中的個股
  }, [onLastSelectedStockCategoryChange]);

  // 處理個股選擇
  const handleStockSelect = useCallback((stockCode: string, stockName: string) => {
    setSelectedStock({ code: stockCode, name: stockName });
  }, []);

  // 處理策略側邊選單點擊
  const handleStrategyCategorySelect = useCallback((category: string) => {
    setSelectedStrategyCategory(category);
  }, []);

  // 根據選擇的類別獲取對應的類股配置
  const getCategoryConfig = useCallback(() => {
    switch (selectedCategory) {
      case 'listed':
        return { code: 'listed', name: '上市19類' };
      case 'otc':
        return { code: 'otc', name: '上櫃類股' };
      case 'futures':
        return { code: 'futures', name: '股票期貨' };
      default:
        return { code: 'listed', name: '上市19類' };
    }
  }, [selectedCategory]);




  return (
    <div className="flex h-full">
      {/* 左側選單 - 根據策略面板狀態切換 */}
      {showStrategyPanel ? (
        <StrategySidebar
          onCategorySelect={handleStrategyCategorySelect}
          selectedCategory={selectedStrategyCategory}
        />
      ) : (
        <TaiwanStockSidebar
          onCategorySelect={handleCategorySelect}
          selectedCategory={selectedCategory}
        />
      )}

      {/* 主要內容區域 */}
      <div className="flex-1 bg-gray-900 p-2">
        {showStrategyPanel ? (
          /* 策略面板內容 */
          <div className="h-full bg-gray-800 border border-gray-700 rounded-lg overflow-hidden relative">
            {/* 關閉按鈕 */}
            <button
              onClick={onCloseStrategyPanel}
              className="absolute top-2 right-2 z-10 w-6 h-6 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-gray-300 hover:text-white transition-colors"
              title="關閉策略面板"
            >
              ×
            </button>
            <StrategyContent selectedCategory={selectedStrategyCategory} />
          </div>
        ) : (
          /* 原有的股票交易面板 */
          <PanelGroup direction="horizontal">
            <Panel defaultSize={40} minSize={20}>
              <PanelGroup direction="vertical">
                <Panel defaultSize={60} minSize={20}>
                  {/* 左上角面板 */}
                  <div className="relative h-full flex flex-col bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
                    <div className="flex-1 overflow-hidden">
                      <StockList
                        selectedCategory={getCategoryConfig()}
                        onCategorySelect={handleStockCategorySelect}
                      />
                    </div>
                  </div>
                </Panel>
                <PanelResizeHandle
                  className="h-px bg-gray-400 data-[resize-handle-state=drag]:bg-cyan-300 data-[resize-handle-state=hover]:bg-cyan-400 transition-colors"
                  tabIndex={-1}
                />
                <Panel defaultSize={40} minSize={20}>
                  {/* 左下角面板 */}
                  <div className="relative h-full flex flex-col bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
                    <div className="flex-1 overflow-auto custom-scrollbar">
                      {lastSelectedStockCategory ? (
                        <StockList
                          selectedCategory={lastSelectedStockCategory}
                          onStockSelect={handleStockSelect}
                        />
                      ) : (
                        <div className="p-4 text-center text-gray-500">請先從上方選擇一個類股</div>
                      )}
                    </div>
                  </div>
                </Panel>
              </PanelGroup>
            </Panel>
            <PanelResizeHandle
              className="w-px bg-gray-400 data-[resize-handle-state=drag]:bg-cyan-300 data-[resize-handle-state=hover]:bg-cyan-400 transition-colors"
              tabIndex={-1}
            />
            <Panel defaultSize={60} minSize={30}>
              {/* 右側整合面板 */}
              <div className="relative h-full bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
                <RightPanelContainer selectedStock={selectedStock} />
              </div>
            </Panel>
          </PanelGroup>
        )}
      </div>
    </div>
  );
};

export default TaiwanStockPageWithSplitPane;