import React from 'react';
import TaskCard from './TaskCard';
import { 
  Plus,
  MoreHorizontal,
  TrendingUp,
  Clock,
  CheckCircle2,
  AlertCircle
} from 'lucide-react';

interface Task {
  id: string;
  title: string;
  description: string;
  status: 'todo' | 'in-progress' | 'review' | 'done';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignee: {
    name: string;
    avatar: string;
    initials: string;
  };
  dueDate: string;
  project: string;
  tags: string[];
  comments: number;
  attachments: number;
  progress?: number;
}

const TaskBoard = () => {
  const tasks: Task[] = [
    {
      id: '1',
      title: 'Design new landing page hero section',
      description: 'Create a compelling hero section that showcases our product value proposition with modern design elements.',
      status: 'in-progress',
      priority: 'high',
      assignee: { name: '<PERSON>', avatar: '', initials: 'SC' },
      dueDate: 'Dec 15',
      project: 'Website Redesign',
      tags: ['Design', 'Frontend', 'Marketing'],
      comments: 8,
      attachments: 3,
      progress: 65
    },
    {
      id: '2',
      title: 'Implement user authentication flow',
      description: 'Build secure login/signup system with OAuth integration and password reset functionality.',
      status: 'todo',
      priority: 'urgent',
      assignee: { name: '<PERSON>', avatar: '', initials: 'MJ' },
      dueDate: 'Dec 18',
      project: 'Auth System',
      tags: ['Backend', 'Security', 'API'],
      comments: 4,
      attachments: 1
    },
    {
      id: '3',
      title: 'Write comprehensive API documentation',
      description: 'Document all API endpoints with examples, error codes, and integration guides.',
      status: 'done',
      priority: 'medium',
      assignee: { name: 'Alex Rodriguez', avatar: '', initials: 'AR' },
      dueDate: 'Dec 12',
      project: 'Documentation',
      tags: ['Documentation', 'API', 'Developer Experience'],
      comments: 12,
      attachments: 2
    },
    {
      id: '4',
      title: 'Set up CI/CD pipeline',
      description: 'Configure automated testing, building, and deployment pipeline for production releases.',
      status: 'review',
      priority: 'high',
      assignee: { name: 'Emily Davis', avatar: '', initials: 'ED' },
      dueDate: 'Dec 20',
      project: 'DevOps',
      tags: ['DevOps', 'Automation', 'Infrastructure'],
      comments: 6,
      attachments: 4
    },
    {
      id: '5',
      title: 'Conduct user research interviews',
      description: 'Interview 15 target users to gather insights about pain points and feature requests.',
      status: 'in-progress',
      priority: 'medium',
      assignee: { name: 'John Smith', avatar: '', initials: 'JS' },
      dueDate: 'Dec 16',
      project: 'User Research',
      tags: ['Research', 'UX', 'Customer Insights'],
      comments: 3,
      attachments: 0,
      progress: 40
    },
    {
      id: '6',
      title: 'Optimize database queries',
      description: 'Improve query performance and implement caching strategies for better response times.',
      status: 'todo',
      priority: 'high',
      assignee: { name: 'Lisa Wang', avatar: '', initials: 'LW' },
      dueDate: 'Dec 22',
      project: 'Performance',
      tags: ['Backend', 'Database', 'Performance'],
      comments: 2,
      attachments: 1
    },
    {
      id: '7',
      title: 'Mobile app responsive design',
      description: 'Ensure all components work seamlessly across different mobile device sizes.',
      status: 'review',
      priority: 'medium',
      assignee: { name: 'David Kim', avatar: '', initials: 'DK' },
      dueDate: 'Dec 19',
      project: 'Mobile App',
      tags: ['Mobile', 'Responsive', 'UI/UX'],
      comments: 5,
      attachments: 2
    },
    {
      id: '8',
      title: 'Security audit and penetration testing',
      description: 'Comprehensive security review of the application with vulnerability assessment.',
      status: 'todo',
      priority: 'urgent',
      assignee: { name: 'Rachel Green', avatar: '', initials: 'RG' },
      dueDate: 'Dec 25',
      project: 'Security',
      tags: ['Security', 'Testing', 'Compliance'],
      comments: 1,
      attachments: 0
    }
  ];

  const columns = [
    { 
      id: 'todo', 
      title: 'To Do', 
      icon: Clock, 
      color: 'from-gray-500 to-gray-600',
      bgColor: 'bg-gray-50 dark:bg-gray-800/50',
      borderColor: 'border-gray-200 dark:border-gray-700'
    },
    { 
      id: 'in-progress', 
      title: 'In Progress', 
      icon: TrendingUp, 
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      borderColor: 'border-blue-200 dark:border-blue-800'
    },
    { 
      id: 'review', 
      title: 'Review', 
      icon: AlertCircle, 
      color: 'from-orange-500 to-orange-600',
      bgColor: 'bg-orange-50 dark:bg-orange-900/20',
      borderColor: 'border-orange-200 dark:border-orange-800'
    },
    { 
      id: 'done', 
      title: 'Done', 
      icon: CheckCircle2, 
      color: 'from-green-500 to-green-600',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      borderColor: 'border-green-200 dark:border-green-800'
    }
  ];

  const getTasksByStatus = (status: string) => {
    return tasks.filter(task => task.status === status);
  };

  return (
    <div className="flex-1 overflow-hidden p-8">
      <div className="h-full">
        <div className="grid grid-cols-4 gap-6 h-full">
          {columns.map((column) => {
            const columnTasks = getTasksByStatus(column.id);
            const Icon = column.icon;
            
            return (
              <div key={column.id} className="flex flex-col h-full">
                {/* Column Header */}
                <div className={`${column.bgColor} ${column.borderColor} border rounded-xl p-4 mb-4 shadow-sm`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 bg-gradient-to-r ${column.color} rounded-lg flex items-center justify-center shadow-sm`}>
                        <Icon className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {column.title}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {columnTasks.length} tasks
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-white/70 dark:hover:bg-gray-700/50 rounded-lg transition-all duration-200">
                        <Plus className="h-4 w-4" />
                      </button>
                      <button className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-white/70 dark:hover:bg-gray-700/50 rounded-lg transition-all duration-200">
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Tasks */}
                <div className="flex-1 space-y-4 overflow-y-auto">
                  {columnTasks.map((task) => (
                    <TaskCard key={task.id} task={task} />
                  ))}
                  
                  {/* Add Task Button */}
                  <button className="w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl text-gray-500 dark:text-gray-400 hover:border-gray-400 dark:hover:border-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-all duration-200 hover:bg-gray-50/50 dark:hover:bg-gray-800/30">
                    <Plus className="h-5 w-5 mx-auto mb-2" />
                    <span className="text-sm font-medium">Add Task</span>
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default TaskBoard;