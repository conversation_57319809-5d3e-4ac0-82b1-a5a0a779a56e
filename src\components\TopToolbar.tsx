import React, { useState, useEffect, useRef } from 'react';
import { 
  Maximize2,
  Minimize2,
  X,
  ChevronDown
} from 'lucide-react';

interface TopToolbarProps {
  currentMarket: 'taiwan' | 'us';
  onMarketChange: (market: 'taiwan' | 'us') => void;
  currentPage: 'home' | 'market' | 'watchlist' | 'strategy-settings' | 'execution-status' | 'strategy-development' | 'settings';
  onPageChange: (page: 'home' | 'market' | 'watchlist' | 'strategy-settings' | 'execution-status' | 'strategy-development' | 'settings') => void;
  showStrategyPanel?: boolean;
  onToggleStrategyPanel?: (category: string) => void;
}

const TopToolbar: React.FC<TopToolbarProps> = ({
  currentMarket,
  onMarketChange,
  currentPage,
  onPageChange,
  showStrategyPanel = false,
  onToggleStrategyPanel
}) => {
  const [showMarketDropdown, setShowMarketDropdown] = useState(false);
  const [showMonitoringDropdown, setShowMonitoringDropdown] = useState(false);
  const monitoringDropdownRef = useRef<HTMLDivElement>(null);

  const marketItems = [
    { key: 'taiwan', label: '台股' },
    { key: 'us', label: '美股' }
  ];

  const monitoringItems = [
    { label: '策略設定', page: 'strategy-settings' as const, category: 'strategySettings' },
    { label: '執行狀態', page: 'execution-status' as const, category: 'executionStatus' }
  ];

  const handleMarketSelect = (marketKey: 'taiwan' | 'us') => {
    onMarketChange(marketKey);
    onPageChange('market'); // 確保導航到市場頁面
    setShowMarketDropdown(false);
  };

  return (
    <div className="bg-gray-900 border-b border-gray-700">
      {/* Title Bar */}
      <div className="flex items-center justify-between px-4 py-1 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-blue-500 rounded-sm flex items-center justify-center">
            <span className="text-white text-xs font-bold">C</span>
          </div>
          <span className="text-sm font-medium text-gray-200">
            CHIPO Auto Trading(個人版) [版本 3.12.05 240229][P120416374:已登入]
          </span>
        </div>
        <div className="flex items-center space-x-1">
          <button className="p-1 hover:bg-gray-600 rounded">
            <Minimize2 className="w-4 h-4 text-gray-300" />
          </button>
          <button className="p-1 hover:bg-gray-600 rounded">
            <Maximize2 className="w-4 h-4 text-gray-300" />
          </button>
          <button className="p-1 hover:bg-red-500 hover:text-white rounded">
            <X className="w-4 h-4 text-gray-300" />
          </button>
        </div>
      </div>

      {/* Function Buttons */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-900 border-b border-gray-700 relative">
        <div className="flex items-center space-x-1">
          <button
            className={`px-3 py-1 rounded text-xs ${
              currentPage === 'home' ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'hover:bg-gray-700 text-gray-300'
            }`}
            onClick={() => onPageChange('home')}
          >
            首頁
          </button>
          
          {/* 大盤 with Dropdown */}
          <div className="relative">
            <button
              className={`px-3 py-1 rounded text-xs flex items-center space-x-1 ${
                currentPage === 'market' && !showStrategyPanel ? 'bg-green-600 hover:bg-green-700 text-white' : 'hover:bg-gray-700 text-gray-300'
              }`}
              onClick={(e) => {
                e.preventDefault();
                if (showMarketDropdown) {
                  setShowMarketDropdown(false);
                } else {
                  onPageChange('market');
                  setShowMarketDropdown(true);
                }
              }}
              onBlur={() => setTimeout(() => setShowMarketDropdown(false), 150)}
            >
              <span>大盤 ({currentMarket === 'taiwan' ? '台股' : '美股'})</span>
              <ChevronDown className={`w-3 h-3 transition-transform ${showMarketDropdown ? 'rotate-180' : ''}`} />
            </button>
            
            {/* Market Dropdown Menu */}
            {showMarketDropdown && (
              <div className="absolute top-full left-0 mt-1 bg-gray-800 border border-gray-600 rounded shadow-lg z-50 min-w-20">
                {marketItems.map((item) => (
                  <button
                    key={item.key}
                    className={`block w-full px-3 py-2 text-left text-xs hover:bg-gray-700 hover:text-gray-200 first:rounded-t last:rounded-b ${
                      currentMarket === item.key ? 'bg-green-600 text-white' : 'text-gray-300'
                    }`}
                    onClick={() => handleMarketSelect(item.key as 'taiwan' | 'us')}
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            )}
          </div>
          
          {/* 實盤監測 with Dropdown */}
          <div className="relative">
            <button
              className={`px-3 py-1 rounded text-xs flex items-center space-x-1 ${
                currentPage === 'strategy-settings' || currentPage === 'execution-status' ? 'bg-purple-600 hover:bg-purple-700 text-white' : 'hover:bg-gray-200 text-gray-700'
              }`}
              onClick={(e) => {
                e.preventDefault();
                if (showMonitoringDropdown) {
                  setShowMonitoringDropdown(false);
                } else {
                  // 預設導航到策略設定頁面
                  onPageChange('strategy-settings');
                  setShowMonitoringDropdown(true);
                }
              }}
              onBlur={() => setTimeout(() => setShowMonitoringDropdown(false), 150)}
            >
              <span>實盤監測</span>
              <ChevronDown className={`w-3 h-3 transition-transform ${showMonitoringDropdown ? 'rotate-180' : ''}`} />
            </button>

            {/* Monitoring Dropdown Menu */}
            {showMonitoringDropdown && (
              <div className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded shadow-lg z-50 min-w-24">
                {monitoringItems.map((item, index) => (
                  <button
                    key={index}
                    className="block w-full px-3 py-2 text-left text-xs text-gray-700 hover:bg-gray-100 hover:text-gray-900 first:rounded-t last:rounded-b"
                    onClick={() => {
                      setShowMonitoringDropdown(false);
                      onPageChange(item.page);
                      console.log(`Selected: ${item.label}`);
                    }}
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* 實盤監測改良 */}
          <button
            className={`px-3 py-1 rounded text-xs ${
              showStrategyPanel ? 'bg-orange-600 hover:bg-orange-700 text-white' : 'hover:bg-gray-200 text-gray-700'
            }`}
            onClick={() => {
              if (onToggleStrategyPanel) {
                onToggleStrategyPanel('strategySettings');
              }
            }}
          >
            實盤監測改良
          </button>

          {/* 策略開發 */}
          <button
            className={`px-3 py-1 rounded text-xs ${
              currentPage === 'strategy-development' ? 'bg-indigo-600 hover:bg-indigo-700 text-white' : 'hover:bg-gray-200 text-gray-700'
            }`}
            onClick={() => onPageChange('strategy-development')}
          >
            策略開發
          </button>
          
          <button
            className={`px-3 py-1 rounded text-xs ${
              currentPage === 'settings' ? 'bg-purple-600 hover:bg-purple-700 text-white' : 'hover:bg-gray-200 text-gray-700'
            }`}
            onClick={() => onPageChange('settings')}
          >
            基本設定
          </button>
        </div>


      </div>
    </div>
  );
};

export default TopToolbar;
