@import "tailwindcss";

@theme {
  --color-background: #111827;
  --color-foreground: #f9fafb;
  --color-card: #1f2937;
  --color-card-foreground: #f9fafb;
  --color-popover: #1f2937;
  --color-popover-foreground: #f9fafb;
  --color-primary: #10b981;
  --color-primary-foreground: #ffffff;
  --color-secondary: #374151;
  --color-secondary-foreground: #f9fafb;
  --color-muted: #374151;
  --color-muted-foreground: #9ca3af;
  --color-accent: #374151;
  --color-accent-foreground: #f9fafb;
  --color-destructive: #ef4444;
  --color-destructive-foreground: #ffffff;
  --color-border: #374151;
  --color-input: #374151;
  --color-ring: #10b981;
  --radius: 0.5rem;
}

/* Custom Scrollbar Styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #10B981 #1F2937;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #1F2937;
  border-radius: 0;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #10B981;
  border-radius: 0;
  border: none;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #34D399;
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: #1F2937;
}

/* 垂直文字樣式 */
.writing-mode-vertical {
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.text-vertical-rl {
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

/* 針對表格容器的特殊處理 - 確保表格完全填滿容器 */
.table-container {
  position: relative;
  overflow: auto;
  overflow-y: scroll; /* 強制顯示垂直滾動條 */
  width: 100%;
  height: 100%;
  scrollbar-width: auto; /* Firefox */
  scrollbar-color: #10B981 #1F2937; /* Firefox - 綠色滾動條 */
}

.table-container table {
  width: 100%;
  min-width: 100%;
  table-layout: fixed;
}

/* 精緻的綠色滾動條樣式 */
.table-container::-webkit-scrollbar {
  width: 2.5px !important;
  height: 2.5px !important;
}

.table-container::-webkit-scrollbar-track {
  background: #1F2937 !important;
  border-radius: 4px !important;
}

.table-container::-webkit-scrollbar-thumb {
  background: #10B981 !important;
  border-radius: 4px !important;
  border: 1px solid #059669 !important;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #34D399 !important;
}

.table-container::-webkit-scrollbar-corner {
  background: #1F2937 !important;
}

/* react-resizable-panels 樣式自定義 */
[data-panel-group] {
  display: flex;
}

[data-panel-group][data-panel-group-direction="vertical"] {
  flex-direction: column;
}

[data-panel-group][data-panel-group-direction="horizontal"] {
  flex-direction: row;
}

[data-panel] {
  flex: 1 1 0%;
  overflow: hidden;
}

[data-panel-resize-handle-enabled] {
  cursor: col-resize;
}

[data-panel-resize-handle-enabled][data-panel-resize-handle-direction="vertical"] {
  cursor: row-resize;
}

[data-resize-handle] {
  background: #374151;
  transition: background-color 0.2s;
}

[data-resize-handle]:hover {
  background: #3B82F6;
}

[data-resize-handle][data-resize-handle-direction="vertical"] {
  height: 1px;
  cursor: row-resize;
}

[data-resize-handle][data-resize-handle-direction="horizontal"] {
  width: 1px;
  cursor: col-resize;
}

/* 面板邊框樣式 */
.panel-border {
  border: 1px solid #374151;
  border-radius: 8px;
  overflow: hidden;
}

/* 修復 PanelIndexDisplay 的定位問題 */
.panel-index-display {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 20;
  pointer-events: none;
}
