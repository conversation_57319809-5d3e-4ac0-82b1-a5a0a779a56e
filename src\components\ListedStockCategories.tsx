import React from 'react';

const ListedStockCategories = () => {
  const listedCategories = [
    { code: '▶▶水泥', name: '水泥', price: '137.41c', change: '▲0.92', changePercent: '+0.67', volume: '8.66', volumePercent: '0.2230' },
    { code: '食品', name: '食品', price: '2080.68c', change: '▲26.17', changePercent: '+1.27', volume: '44.20', volumePercent: '1.1390' },
    { code: '塑膠', name: '塑膠', price: '98.96c', change: '▲1.77', changePercent: '+1.82', volume: '29.22', volumePercent: '0.7529' },
    { code: '紡織纖維', name: '紡織纖維', price: '527.76c', change: '▲5.41', changePercent: '+1.04', volume: '11.83', volumePercent: '0.3049' },
    { code: '電機機械', name: '電機機械', price: '375.75c', change: '▲2.73', changePercent: '+0.73', volume: '198.36', volumePercent: '5.1115' },
    { code: '電器電纜', name: '電器電纜', price: '84.50c', change: '▲0.71', changePercent: '+0.85', volume: '15.22', volumePercent: '0.3920' },
    { code: '化學生技醫療', name: '化學生技醫療', price: '134.65c', change: '▲1.41', changePercent: '+1.06', volume: '45.13', volumePercent: '1.1630' },
    { code: '玻璃陶瓷', name: '玻璃陶瓷', price: '45.29c', change: '▲0.36', changePercent: '+0.80', volume: '1.67', volumePercent: '0.0429' },
    { code: '造紙', name: '造紙', price: '240.69c', change: '▲2.71', changePercent: '+1.14', volume: '1.45', volumePercent: '0.0374' },
    { code: '鋼鐵', name: '鋼鐵', price: '117.53c', change: '▲1.27', changePercent: '+1.09', volume: '25.82', volumePercent: '0.6654' },
    { code: '橡膠', name: '橡膠', price: '221.31c', change: '▲4.39', changePercent: '+2.02', volume: '5.60', volumePercent: '0.1444' },
    { code: '汽車', name: '汽車', price: '330.96c', change: '▲5.34', changePercent: '+1.64', volume: '30.24', volumePercent: '0.7792' },
    { code: '電子', name: '電子', price: '1253.88c', change: '▲0.98', changePercent: '+0.08', volume: '2714.08', volumePercent: '69.9383' },
    { code: '建材營造', name: '建材營造', price: '507.25c', change: '▲7.62', changePercent: '+1.53', volume: '23.14', volumePercent: '0.5962' },
    { code: '航運業', name: '航運業', price: '193.16c', change: '▼0.27', changePercent: '-0.14', volume: '98.25', volumePercent: '2.5318' },
    { code: '觀光餐旅', name: '觀光餐旅', price: '113.96c', change: '▲1.54', changePercent: '+1.37', volume: '4.93', volumePercent: '0.1269' },
    { code: '金融保險', name: '金融保險', price: '2157.97c', change: '▲10.47', changePercent: '+0.49', volume: '228.56', volumePercent: '5.8895' },
    { code: '貿易百貨', name: '貿易百貨', price: '258.72c', change: '▲1.05', changePercent: '+0.41', volume: '6.56', volumePercent: '0.1690' },
    { code: '其他', name: '其他', price: '292.50c', change: '▲1.97', changePercent: '+0.68', volume: '25.58', volumePercent: '0.6592' },
  ];

  return (
    <div className="h-full bg-gray-900">
      <div className="bg-gray-800 px-3 py-2 border-b border-gray-700">
        <h3 className="text-white text-sm font-semibold">上市19類</h3>
      </div>
      <div className="overflow-auto h-full custom-scrollbar">
        <table className="w-full text-xs">
          <thead className="bg-gray-800 sticky top-0">
            <tr className="text-gray-300">
              <th className="px-2 py-1 text-left border-r border-gray-700">商品</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">成交</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">漲跌</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">漲跌%</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">成交值</th>
              <th className="px-2 py-1 text-right">成交比重%</th>
            </tr>
          </thead>
          <tbody>
            {listedCategories.map((category, index) => (
              <tr key={index} className="hover:bg-gray-800 border-b border-gray-800">
                <td className="px-2 py-1 text-white border-r border-gray-700 font-medium">{category.name}</td>
                <td className={`px-2 py-1 text-right border-r border-gray-700 font-medium ${
                  category.change.includes('▲') ? 'text-red-400' : 
                  category.change.includes('▼') ? 'text-green-400' : 'text-white'
                }`}>{category.price}</td>
                <td className={`px-2 py-1 text-right border-r border-gray-700 ${
                  category.change.includes('▲') ? 'text-red-400' : 
                  category.change.includes('▼') ? 'text-green-400' : 'text-white'
                }`}>{category.change}</td>
                <td className={`px-2 py-1 text-right border-r border-gray-700 ${
                  category.changePercent.includes('+') ? 'text-red-400' : 
                  category.changePercent.includes('-') ? 'text-green-400' : 'text-white'
                }`}>{category.changePercent}</td>
                <td className="px-2 py-1 text-white text-right border-r border-gray-700">{category.volume}</td>
                <td className="px-2 py-1 text-white text-right">{category.volumePercent}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ListedStockCategories;