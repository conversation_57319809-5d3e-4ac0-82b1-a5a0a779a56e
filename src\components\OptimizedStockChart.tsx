import React, { useMemo } from 'react';
import StreamableWindow from './StreamableWindow';
import { useWindowData } from '../hooks/useWindowData';

interface OptimizedStockChartProps {
  onCategorySelect?: (categoryCode: string, categoryName: string) => void;
}

const OptimizedStockChart: React.FC<OptimizedStockChartProps> = ({ onCategorySelect }) => {
  const windowId = 'stock-chart-main';
  
  const {
    data: streamData,
    isLoading,
    error
  } = useWindowData({
    windowId,
    type: 'chart',
    updateInterval: 1000,
    priority: 'high'
  });

  // 模擬股票類別數據 - 使用 useMemo 避免重複創建
  const listedCategories = useMemo(() => [
    { code: 'water', name: '水泥', price: '137.41c', change: '▲0.92', changePercent: '+0.67', volume: '8.66', volumePercent: '0.2230' },
    { code: 'food', name: '食品', price: '2080.68c', change: '▲26.17', changePercent: '+1.27', volume: '44.20', volumePercent: '1.1390' },
    { code: 'plastic', name: '塑膠', price: '98.96c', change: '▲1.77', changePercent: '+1.82', volume: '29.22', volumePercent: '0.7529' },
    { code: 'electronics', name: '電子', price: '1253.88c', change: '▲0.98', changePercent: '+0.08', volume: '2714.08', volumePercent: '69.9383' },
    { code: 'finance', name: '金融保險', price: '2157.97c', change: '▲10.47', changePercent: '+0.49', volume: '228.56', volumePercent: '5.8895' },
  ], []);

  // 優化的表格行組件
  const CategoryRow = React.memo(({ category }: { category: typeof listedCategories[0] }) => (
    <tr 
      className="hover:bg-gray-800 border-b border-gray-800 cursor-pointer transition-colors duration-200"
      onClick={() => onCategorySelect?.(category.code, category.name)}
    >
      <td className="px-2 py-1 text-white font-medium border-r border-gray-700">
        ● {category.name}
      </td>
      <td className={`px-2 py-1 text-right border-r border-gray-700 font-medium ${
        category.change.includes('▲') ? 'text-red-400' : 
        category.change.includes('▼') ? 'text-green-400' : 'text-white'
      }`}>
        {category.price}
      </td>
      <td className={`px-2 py-1 text-right border-r border-gray-700 ${
        category.change.includes('▲') ? 'text-red-400' : 
        category.change.includes('▼') ? 'text-green-400' : 'text-white'
      }`}>
        {category.change}
      </td>
      <td className={`px-2 py-1 text-right border-r border-gray-700 ${
        category.changePercent.includes('+') ? 'text-red-400' : 
        category.changePercent.includes('-') ? 'text-green-400' : 'text-white'
      }`}>
        {category.changePercent}
      </td>
      <td className="px-2 py-1 text-white text-right border-r border-gray-700">
        {category.volume}
      </td>
      <td className="px-2 py-1 text-white text-right">
        {category.volumePercent}
      </td>
    </tr>
  ));

  return (
    <StreamableWindow
      windowId={windowId}
      type="chart"
      title="上市19類"
      updateInterval={1000}
      priority="high"
      showControls={true}
    >
      <div className="h-full overflow-auto custom-scrollbar">
        <table className="w-full text-xs">
          <thead className="bg-gray-800 sticky top-0">
            <tr className="text-gray-300">
              <th className="px-2 py-1 text-left border-r border-gray-700">商品</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">成交</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">漲跌</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">漲跌%</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">成交值</th>
              <th className="px-2 py-1 text-right">成交比重%</th>
            </tr>
          </thead>
          <tbody>
            {listedCategories.map((category) => (
              <CategoryRow key={category.code} category={category} />
            ))}
          </tbody>
        </table>
      </div>
    </StreamableWindow>
  );
};

export default OptimizedStockChart;