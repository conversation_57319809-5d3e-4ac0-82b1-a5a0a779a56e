import React, { useState, useRef, useCallback } from 'react';
import { Plus, Search, Filter, Star, TrendingUp, TrendingDown, BarChart3 } from 'lucide-react';
import { usePanelContext } from '../contexts/PanelContext';

const WatchlistPage = () => {
  const { panels, updatePanel, getPanelById } = usePanelContext();
  
  // 修改為三欄布局 - 左側兩個面板，右側一個合併面板
  const [panelSizes, setPanelSizes] = useState({
    topLeft: { width: 50, height: 50 },
    bottomLeft: { width: 50, height: 50 },
    right: { width: 50, height: 100 }, // 右側合併面板佔據整個右側
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<string | null>(null);

  const handleMouseDown = useCallback((divider: string) => {
    setIsDragging(divider);
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    setPanelSizes(prev => {
      const newSizes = { ...prev };
      
      if (isDragging === 'vertical') {
        // 垂直分割線 - 調整左側和右側的寬度
        const newLeftWidth = Math.max(20, Math.min(80, x));
        const newRightWidth = 100 - newLeftWidth;
        
        newSizes.topLeft.width = newLeftWidth;
        newSizes.bottomLeft.width = newLeftWidth;
        newSizes.right.width = newRightWidth;
      } else if (isDragging === 'horizontal') {
        // 水平分割線 - 只調整左側上下面板的高度，右側保持100%
        const newTopHeight = Math.max(20, Math.min(80, y));
        const newBottomHeight = 100 - newTopHeight;
        
        newSizes.topLeft.height = newTopHeight;
        newSizes.bottomLeft.height = newBottomHeight;
        // 右側面板保持100%高度
      }
      
      return newSizes;
    });
  }, [isDragging]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(null);
  }, []);

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = isDragging === 'vertical' ? 'col-resize' : 'row-resize';
      document.body.style.userSelect = 'none';
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 空白面板組件
  const EmptyPanel = ({ title, description, icon: Icon }: {
    title: string;
    description: string;
    icon: React.ComponentType<any>;
  }) => (
    <div className="h-full bg-gray-900 border border-gray-700 rounded-lg relative">
      <div className="h-full flex flex-col items-center justify-center p-6">
        <div className="bg-gray-800 rounded-full p-4 mb-4">
          <Icon className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-white text-lg font-semibold mb-2">{title}</h3>
        <p className="text-gray-400 text-sm text-center mb-6 max-w-xs">
          {description}
        </p>
        <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
          <Plus className="w-4 h-4" />
          <span>開始設定</span>
        </button>
      </div>
    </div>
  );

  // 右側合併面板組件 - 包含漲幅排行和投資組合功能
  const RightCombinedPanel = () => (
    <div className="h-full bg-gray-900 border border-gray-700 rounded-lg relative">
      <div className="h-full flex flex-col">
        {/* 上半部 - 漲幅排行（原B1-自選3） */}
        <div className="flex-1 border-b border-gray-700">
          <div className="h-full flex flex-col items-center justify-center p-6">
            <div className="bg-gray-800 rounded-full p-4 mb-4">
              <TrendingUp className="w-8 h-8 text-green-400" />
            </div>
            <h3 className="text-white text-lg font-semibold mb-2">漲幅排行</h3>
            <p className="text-gray-400 text-sm text-center mb-6 max-w-xs">
              查看今日漲幅最大的股票，發掘投資機會
            </p>
            <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
              <TrendingUp className="w-4 h-4" />
              <span>查看漲幅</span>
            </button>
          </div>
        </div>

        {/* 下半部 - 投資組合（原B1-自選4） */}
        <div className="flex-1">
          <div className="h-full flex flex-col items-center justify-center p-6">
            <div className="bg-gray-800 rounded-full p-4 mb-4">
              <BarChart3 className="w-8 h-8 text-blue-400" />
            </div>
            <h3 className="text-white text-lg font-semibold mb-2">投資組合</h3>
            <p className="text-gray-400 text-sm text-center mb-6 max-w-xs">
              管理您的持股組合，追蹤投資績效表現
            </p>
            <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
              <BarChart3 className="w-4 h-4" />
              <span>管理組合</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div 
      ref={containerRef}
      className="flex-1 relative bg-gray-900 overflow-hidden p-4"
      style={{ minHeight: '400px' }}
      data-container-id="watchlist-area"
    >
      {/* 頁面標題 */}
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-2">
              <Star className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">自選股</h1>
              <p className="text-gray-400 text-sm">管理您的投資組合和關注清單</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors">
              <Search className="w-5 h-5" />
            </button>
            <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors">
              <Filter className="w-5 h-5" />
            </button>
            <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
              <Plus className="w-4 h-4" />
              <span>新增股票</span>
            </button>
          </div>
        </div>
      </div>

      {/* 三個面板區域 */}
      <div className="h-full relative" style={{ height: 'calc(100% - 100px)' }}>
        {/* 左上角面板 - B1-自選1 */}
        <div 
          className="absolute"
          style={{
            left: 0,
            top: 0,
            width: `${panelSizes.topLeft.width}%`,
            height: `${panelSizes.topLeft.height}%`,
            paddingRight: '8px',
            paddingBottom: '8px'
          }}
          data-panel-id="watchlist-top-left"
        >
          <EmptyPanel
            title="我的自選股"
            description="建立您的個人股票關注清單，追蹤感興趣的投資標的"
            icon={Star}
          />
        </div>

        {/* 左下角面板 - B1-自選2 */}
        <div 
          className="absolute"
          style={{
            left: 0,
            bottom: 0,
            width: `${panelSizes.bottomLeft.width}%`,
            height: `${panelSizes.bottomLeft.height}%`,
            paddingRight: '8px',
            paddingTop: '8px'
          }}
          data-panel-id="watchlist-bottom-left"
        >
          <EmptyPanel
            title="跌幅排行"
            description="監控跌幅較大的股票，評估風險與機會"
            icon={TrendingDown}
          />
        </div>

        {/* 右側合併面板 - B1-自選3 (包含原B1-自選3和B1-自選4的功能) */}
        <div 
          className="absolute"
          style={{
            right: 0,
            top: 0,
            width: `${panelSizes.right.width}%`,
            height: `${panelSizes.right.height}%`,
            paddingLeft: '8px'
          }}
          data-panel-id="watchlist-right"
        >
          <RightCombinedPanel />
        </div>

        {/* 垂直分割線 - 分隔左側和右側 */}
        <div
          className="absolute top-0 bottom-0 w-1 bg-gray-600 hover:bg-blue-500 cursor-col-resize z-10 transition-colors"
          style={{
            left: `${panelSizes.topLeft.width}%`,
            transform: 'translateX(-50%)'
          }}
          onMouseDown={() => handleMouseDown('vertical')}
          data-divider-id="vertical-divider"
          data-divider-type="vertical"
        >
          <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-3 hover:bg-blue-500/20" />
        </div>

        {/* 水平分割線 - 只分隔左側上下面板 */}
        <div
          className="absolute left-0 h-1 bg-gray-600 hover:bg-blue-500 cursor-row-resize z-10 transition-colors"
          style={{
            top: `${panelSizes.topLeft.height}%`,
            width: `${panelSizes.topLeft.width}%`,
            transform: 'translateY(-50%)'
          }}
          onMouseDown={() => handleMouseDown('horizontal')}
          data-divider-id="horizontal-divider"
          data-divider-type="horizontal"
        >
          <div className="absolute inset-x-0 top-1/2 transform -translate-y-1/2 h-3 hover:bg-blue-500/20" />
        </div>

        {/* 拖拽時的覆蓋層 */}
        {isDragging && (
          <div className="absolute inset-0 z-20 bg-transparent" data-overlay-active="true" />
        )}
      </div>
    </div>
  );
};

export default WatchlistPage;