import { IndexConfig } from './IndexTypes';

export interface PanelConfig {
  id: string;
  title: string;
  component: string;
  position: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';
  size: {
    width: number;
    height: number;
  };
  isVisible: boolean;
  isResizable: boolean;
  isDraggable: boolean;
  zIndex: number;
  // 新增索引配置
  index?: IndexConfig;
  displayCode?: string;
}

export interface PanelSizes {
  topLeft: { width: number; height: number };
  topRight: { width: number; height: number };
  bottomLeft: { width: number; height: number };
  bottomRight: { width: number; height: number };
}

export type PanelPosition = 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';

export interface PanelContextType {
  panels: Record<string, PanelConfig>;
  updatePanel: (id: string, updates: Partial<PanelConfig>) => void;
  getPanelById: (id: string) => PanelConfig | undefined;
  getPanelsByPosition: (position: PanelPosition) => PanelConfig[];
  swapPanels: (id1: string, id2: string) => void;
  resetPanels: () => void;
  // 新增索引相關方法
  getPanelByIndexCode: (indexCode: string) => PanelConfig | undefined;
  updatePanelIndex: (id: string, indexCode: string) => void;
  // 新增台股類股更新方法
  updateTaiwanCategoryPanel?: (categoryType: 'listed' | 'otc' | 'futures') => void;
}