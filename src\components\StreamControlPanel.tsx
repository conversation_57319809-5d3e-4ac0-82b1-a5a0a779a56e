import React, { useState } from 'react';
import { Play, Pause, Settings, Activity, Zap, Clock, AlertCircle } from 'lucide-react';
import { useWindowStream } from '../contexts/WindowStreamContext';

const StreamControlPanel: React.FC = () => {
  const {
    windows,
    streamConfigs,
    pauseAllStreams,
    resumeAllStreams,
    configureStream
  } = useWindowStream();

  const [isExpanded, setIsExpanded] = useState(false);
  const [globalPaused, setGlobalPaused] = useState(false);

  // 計算統計資訊
  const totalWindows = Object.keys(windows).length;
  const activeStreams = Object.values(streamConfigs).filter(config => config.enabled).length;
  const loadingWindows = Object.values(windows).filter(window => window.isLoading).length;
  const errorWindows = Object.values(windows).filter(window => window.error).length;

  // 全域暫停/恢復
  const handleGlobalToggle = () => {
    if (globalPaused) {
      resumeAllStreams();
      setGlobalPaused(false);
    } else {
      pauseAllStreams();
      setGlobalPaused(true);
    }
  };

  // 獲取優先級圖示
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return <Zap className="w-3 h-3 text-red-400" />;
      case 'low': return <Clock className="w-3 h-3 text-green-400" />;
      default: return <Activity className="w-3 h-3 text-yellow-400" />;
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* 主控制按鈕 */}
      <div className="bg-gray-800 border border-gray-600 rounded-lg shadow-lg">
        <div className="flex items-center space-x-2 px-3 py-2">
          {/* 狀態指示器 */}
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${globalPaused ? 'bg-red-500' : 'bg-green-500'}`} />
            <span className="text-xs text-gray-300">
              {activeStreams}/{totalWindows}
            </span>
          </div>

          {/* 載入中指示器 */}
          {loadingWindows > 0 && (
            <div className="flex items-center space-x-1">
              <Activity className="w-3 h-3 text-blue-400 animate-pulse" />
              <span className="text-xs text-blue-400">{loadingWindows}</span>
            </div>
          )}

          {/* 錯誤指示器 */}
          {errorWindows > 0 && (
            <div className="flex items-center space-x-1">
              <AlertCircle className="w-3 h-3 text-red-400" />
              <span className="text-xs text-red-400">{errorWindows}</span>
            </div>
          )}

          {/* 全域控制按鈕 */}
          <button
            onClick={handleGlobalToggle}
            className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            title={globalPaused ? '恢復所有數據流' : '暫停所有數據流'}
          >
            {globalPaused ? (
              <Play className="w-4 h-4" />
            ) : (
              <Pause className="w-4 h-4" />
            )}
          </button>

          {/* 展開/收合按鈕 */}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            title="展開詳細控制"
          >
            <Settings className="w-4 h-4" />
          </button>
        </div>

        {/* 詳細控制面板 */}
        {isExpanded && (
          <div className="border-t border-gray-600 p-3 max-h-64 overflow-y-auto custom-scrollbar">
            <h4 className="text-sm font-medium text-white mb-2">視窗狀態</h4>
            <div className="space-y-2">
              {Object.entries(windows).map(([windowId, window]) => {
                const config = streamConfigs[windowId];
                return (
                  <div key={windowId} className="flex items-center justify-between text-xs">
                    <div className="flex items-center space-x-2">
                      {getPriorityIcon(config?.priority || 'medium')}
                      <span className="text-gray-300 truncate max-w-20" title={windowId}>
                        {windowId}
                      </span>
                      {window.isLoading && (
                        <Activity className="w-3 h-3 text-blue-400 animate-pulse" />
                      )}
                      {window.error && (
                        <AlertCircle className="w-3 h-3 text-red-400" />
                      )}
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className={`w-2 h-2 rounded-full ${
                        config?.enabled ? 'bg-green-500' : 'bg-gray-500'
                      }`} />
                      <span className="text-gray-400">
                        {config?.updateInterval || 1000}ms
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StreamControlPanel;