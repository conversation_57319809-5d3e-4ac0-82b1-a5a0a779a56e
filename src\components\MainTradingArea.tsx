import React, { useState, useRef, useCallback } from 'react';
import Stock<PERSON>hart from './StockChart';
import StockList from './StockList';
import MarketData from './MarketData';
import TechnicalAnalysis from './TechnicalAnalysis';

const MainTradingArea = () => {
  // 初始面板大小 (百分比)
  const [panelSizes, setPanelSizes] = useState({
    topLeft: { width: 60, height: 60 },
    topRight: { width: 40, height: 60 },
    bottomLeft: { width: 60, height: 40 },
    bottomRight: { width: 40, height: 40 }
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<string | null>(null);

  const handleMouseDown = useCallback((divider: string) => {
    setIsDragging(divider);
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    setPanelSizes(prev => {
      const newSizes = { ...prev };
      
      if (isDragging === 'vertical') {
        // 垂直分割線 - 調整左右寬度
        const newLeftWidth = Math.max(20, Math.min(80, x));
        const newRightWidth = 100 - newLeftWidth;
        
        newSizes.topLeft.width = newLeftWidth;
        newSizes.bottomLeft.width = newLeftWidth;
        newSizes.topRight.width = newRightWidth;
        newSizes.bottomRight.width = newRightWidth;
      } else if (isDragging === 'horizontal') {
        // 水平分割線 - 調整上下高度
        const newTopHeight = Math.max(20, Math.min(80, y));
        const newBottomHeight = 100 - newTopHeight;
        
        newSizes.topLeft.height = newTopHeight;
        newSizes.topRight.height = newTopHeight;
        newSizes.bottomLeft.height = newBottomHeight;
        newSizes.bottomRight.height = newBottomHeight;
      }
      
      return newSizes;
    });
  }, [isDragging]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(null);
  }, []);

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = isDragging === 'vertical' ? 'col-resize' : 'row-resize';
      document.body.style.userSelect = 'none';
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return (
    <div 
      ref={containerRef}
      className="flex-1 relative bg-gray-900 overflow-hidden"
      style={{ minHeight: '400px' }}
    >
      {/* 左上角面板 - 主圖表 */}
      <div 
        className="absolute bg-gray-900 border-r border-b border-gray-700"
        style={{
          left: 0,
          top: 0,
          width: `${panelSizes.topLeft.width}%`,
          height: `${panelSizes.topLeft.height}%`
        }}
      >
        <div className="h-full p-2">
          <StockChart />
        </div>
      </div>

      {/* 右上角面板 - 市場數據 */}
      <div 
        className="absolute bg-gray-900 border-b border-gray-700"
        style={{
          right: 0,
          top: 0,
          width: `${panelSizes.topRight.width}%`,
          height: `${panelSizes.topRight.height}%`
        }}
      >
        <div className="h-full">
          <MarketData />
        </div>
      </div>

      {/* 左下角面板 - 股票列表 */}
      <div 
        className="absolute bg-gray-900 border-r border-gray-700"
        style={{
          left: 0,
          bottom: 0,
          width: `${panelSizes.bottomLeft.width}%`,
          height: `${panelSizes.bottomLeft.height}%`
        }}
      >
        <div className="h-full">
          <StockList />
        </div>
      </div>

      {/* 右下角面板 - 技術分析 */}
      <div 
        className="absolute bg-gray-900"
        style={{
          right: 0,
          bottom: 0,
          width: `${panelSizes.bottomRight.width}%`,
          height: `${panelSizes.bottomRight.height}%`
        }}
      >
        <div className="h-full">
          <TechnicalAnalysis />
        </div>
      </div>

      {/* 垂直分割線 */}
      <div
        className="absolute top-0 bottom-0 w-1 bg-gray-600 hover:bg-blue-500 cursor-col-resize z-10 transition-colors"
        style={{
          left: `${panelSizes.topLeft.width}%`,
          transform: 'translateX(-50%)'
        }}
        onMouseDown={() => handleMouseDown('vertical')}
      >
        <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-3 hover:bg-blue-500/20" />
      </div>

      {/* 水平分割線 */}
      <div
        className="absolute left-0 right-0 h-1 bg-gray-600 hover:bg-blue-500 cursor-row-resize z-10 transition-colors"
        style={{
          top: `${panelSizes.topLeft.height}%`,
          transform: 'translateY(-50%)'
        }}
        onMouseDown={() => handleMouseDown('horizontal')}
      >
        <div className="absolute inset-x-0 top-1/2 transform -translate-y-1/2 h-3 hover:bg-blue-500/20" />
      </div>

      {/* 拖拽時的覆蓋層 */}
      {isDragging && (
        <div className="absolute inset-0 z-20 bg-transparent" />
      )}
    </div>
  );
};

export default MainTradingArea;