import React, { useState } from 'react';
import { ChevronRight, TrendingUp, Building2, Zap } from 'lucide-react';

interface TaiwanStockSidebarProps {
  onCategorySelect: (category: string) => void;
  selectedCategory: string;
}

const TaiwanStockSidebar: React.FC<TaiwanStockSidebarProps> = ({ 
  onCategorySelect, 
  selectedCategory 
}) => {
  const menuItems = [
    {
      id: 'listed',
      label: '上市類股',
      icon: Building2,
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'otc',
      label: '上櫃類股',
      icon: TrendingUp,
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'futures',
      label: '股票期貨',
      icon: Zap,
      color: 'from-purple-500 to-purple-600'
    }
  ];

  return (
    <div className="w-8 bg-gradient-to-b from-gray-100 to-gray-200 border-r border-gray-300 flex flex-col">
      {/* 垂直選單項目 */}
      <div className="flex-1 py-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isSelected = selectedCategory === item.id;
          
          return (
            <button
              key={item.id}
              onClick={() => onCategorySelect(item.id)}
              tabIndex={-1}
              className={`w-full h-20 flex flex-col items-center justify-center text-xs transition-all duration-200 hover:bg-gray-200 relative group ${
                isSelected
                  ? 'bg-gradient-to-b from-blue-100 to-purple-100 border-r-2 border-blue-500'
                  : ''
              }`}
            >
              <div className={`w-6 h-6 bg-gradient-to-r ${item.color} rounded-md flex items-center justify-center shadow-sm mb-1`}>
                <Icon className="h-3 w-3 text-white" />
              </div>
              
              {/* 垂直文字 */}
              <div className="writing-mode-vertical text-vertical-rl">
                <span className={`font-medium text-xs ${isSelected ? 'text-gray-900' : 'text-gray-700'}`}>
                  {item.label}
                </span>
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default TaiwanStockSidebar;