import { useCallback } from 'react';
import { usePanelContext } from '../contexts/PanelContext';
import { PanelConfig } from '../types/PanelTypes';
import { parseIndexCode, generateDisplayCode, validateIndexCode, getCategoryPrefix, getWindowIndexFromPosition } from '../utils/indexUtils';

export const usePanelManager = () => {
  const { 
    panels, 
    updatePanel, 
    getPanelById, 
    getPanelsByPosition, 
    swapPanels, 
    resetPanels,
    getPanelByIndexCode,
    updatePanelIndex,
    updateTaiwanCategoryPanel
  } = usePanelContext();

  // 查找面板的便捷方法
  const findPanel = useCallback((query: string | { id?: string; title?: string; component?: string; position?: string; indexCode?: string }) => {
    if (typeof query === 'string') {
      // 如果是字符串，嘗試按ID、標題、組件名或索引碼查找
      return Object.values(panels).find(panel => 
        panel.id === query || 
        panel.title.includes(query) || 
        panel.component === query ||
        panel.displayCode === query
      );
    } else {
      // 如果是對象，按屬性查找
      return Object.values(panels).find(panel => {
        return (!query.id || panel.id === query.id) &&
               (!query.title || panel.title.includes(query.title)) &&
               (!query.component || panel.component === query.component) &&
               (!query.position || panel.position === query.position) &&
               (!query.indexCode || panel.displayCode === query.indexCode);
      });
    }
  }, [panels]);

  // 批量更新面板
  const updateMultiplePanels = useCallback((updates: Array<{ id: string; updates: Partial<PanelConfig> }>) => {
    updates.forEach(({ id, updates: panelUpdates }) => {
      updatePanel(id, panelUpdates);
    });
  }, [updatePanel]);

  // 隱藏/顯示面板
  const togglePanelVisibility = useCallback((id: string) => {
    const panel = getPanelById(id);
    if (panel) {
      updatePanel(id, { isVisible: !panel.isVisible });
    }
  }, [getPanelById, updatePanel]);

  // 重置特定面板大小
  const resetPanelSize = useCallback((id: string, size: { width: number; height: number }) => {
    updatePanel(id, { size });
  }, [updatePanel]);

  // 獲取所有面板ID列表
  const getAllPanelIds = useCallback(() => {
    return Object.keys(panels);
  }, [panels]);

  // 獲取可見面板列表
  const getVisiblePanels = useCallback(() => {
    return Object.values(panels).filter(panel => panel.isVisible);
  }, [panels]);

  // 根據索引碼查找面板
  const findPanelByIndex = useCallback((indexCode: string) => {
    return getPanelByIndexCode(indexCode);
  }, [getPanelByIndexCode]);

  // 獲取所有索引碼
  const getAllIndexCodes = useCallback(() => {
    return Object.values(panels)
      .map(panel => panel.displayCode)
      .filter(code => code !== undefined) as string[];
  }, [panels]);

  // 驗證並更新面板索引
  const setValidPanelIndex = useCallback((id: string, indexCode: string) => {
    if (validateIndexCode(indexCode)) {
      updatePanelIndex(id, indexCode);
      return true;
    }
    return false;
  }, [updatePanelIndex]);

  // 獲取面板的完整索引信息
  const getPanelIndexInfo = useCallback((id: string) => {
    const panel = getPanelById(id);
    if (panel && panel.index) {
      return {
        panel,
        index: panel.index,
        displayCode: panel.displayCode,
        parsedIndex: parseIndexCode(panel.displayCode || '')
      };
    }
    return null;
  }, [getPanelById]);

  // 更新台股類股面板
  const updateTaiwanCategory = useCallback((categoryType: 'listed' | 'otc' | 'futures') => {
    if (updateTaiwanCategoryPanel) {
      updateTaiwanCategoryPanel(categoryType);
    }
  }, [updateTaiwanCategoryPanel]);

  // 根據類股類型和位置獲取對應的索引碼
  const getTaiwanCategoryIndexCode = useCallback((categoryType: 'listed' | 'otc' | 'futures', position: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight' = 'bottomLeft') => {
    const prefix = getCategoryPrefix(categoryType);
    const windowIndex = getWindowIndexFromPosition(position);
    return `A1-${prefix}${windowIndex}`;
  }, []);

  // 調試用：打印面板信息
  const debugPanelInfo = useCallback((id?: string) => {
    if (id) {
      const panel = getPanelById(id);
      const indexInfo = getPanelIndexInfo(id);
      console.log(`Panel ${id}:`, { panel, indexInfo });
    } else {
      console.log('All panels:', panels);
      console.log('All index codes:', getAllIndexCodes());
      console.log('Taiwan category codes:', {
        listed: getTaiwanCategoryIndexCode('listed'),
        otc: getTaiwanCategoryIndexCode('otc'),
        futures: getTaiwanCategoryIndexCode('futures')
      });
      console.log('Panel layout mapping:', {
        'A1-上市1': 'topLeft (左上角)',
        'A1-上市2': 'bottomLeft (左下角)',
        'A1-上市3': 'topRight (右上角)',
        'A1-上市4': 'bottomRight (右下角)'
      });
    }
  }, [panels, getPanelById, getPanelIndexInfo, getAllIndexCodes, getTaiwanCategoryIndexCode]);

  return {
    panels,
    updatePanel,
    getPanelById,
    getPanelsByPosition,
    swapPanels,
    resetPanels,
    findPanel,
    updateMultiplePanels,
    togglePanelVisibility,
    resetPanelSize,
    getAllPanelIds,
    getVisiblePanels,
    debugPanelInfo,
    // 索引相關方法
    getPanelByIndexCode,
    updatePanelIndex,
    findPanelByIndex,
    getAllIndexCodes,
    setValidPanelIndex,
    getPanelIndexInfo,
    // 台股類股相關方法
    updateTaiwanCategory,
    getTaiwanCategoryIndexCode
  };
};