import { useEffect, useCallback } from 'react';
import { useWindowStream } from '../contexts/WindowStreamContext';

interface UseWindowDataOptions {
  windowId: string;
  type: 'chart' | 'list' | 'market' | 'technical';
  updateInterval?: number;
  priority?: 'high' | 'medium' | 'low';
  autoStart?: boolean;
}

export const useWindowData = (options: UseWindowDataOptions) => {
  const {
    windows,
    streamConfigs,
    updateWindow,
    setWindowLoading,
    setWindowError,
    configureStream,
    pauseStream,
    resumeStream
  } = useWindowStream();

  const {
    windowId,
    type,
    updateInterval = 1000,
    priority = 'medium',
    autoStart = true
  } = options;

  const windowData = windows[windowId];
  const streamConfig = streamConfigs[windowId];

  // 初始化視窗配置
  useEffect(() => {
    configureStream(windowId, {
      updateInterval,
      priority,
      enabled: autoStart
    });

    // 初始化視窗數據
    if (!windowData) {
      updateWindow(windowId, {
        type,
        data: null,
        lastUpdate: 0,
        isLoading: false
      });
    }
  }, [windowId, type, updateInterval, priority, autoStart, configureStream, updateWindow, windowData]);

  // 手動更新數據
  const manualUpdate = useCallback(async () => {
    setWindowLoading(windowId, true);
    try {
      // 這裡可以調用實際的 API
      // const data = await fetchWindowData(windowId, type);
      // updateWindow(windowId, data);
      
      // 暫時使用模擬數據
      setTimeout(() => {
        updateWindow(windowId, {
          type,
          data: { updated: Date.now() },
          lastUpdate: Date.now(),
          isLoading: false
        });
      }, 500);
    } catch (error) {
      setWindowError(windowId, error instanceof Error ? error.message : '更新失敗');
    }
  }, [windowId, type, updateWindow, setWindowLoading, setWindowError]);

  // 暫停/恢復數據流
  const toggleStream = useCallback(() => {
    if (streamConfig?.enabled) {
      pauseStream(windowId);
    } else {
      resumeStream(windowId);
    }
  }, [streamConfig?.enabled, pauseStream, resumeStream, windowId]);

  return {
    data: windowData?.data,
    isLoading: windowData?.isLoading || false,
    error: windowData?.error,
    lastUpdate: windowData?.lastUpdate || 0,
    isStreamEnabled: streamConfig?.enabled || false,
    streamPriority: streamConfig?.priority || 'medium',
    manualUpdate,
    toggleStream,
    pauseStream: () => pauseStream(windowId),
    resumeStream: () => resumeStream(windowId)
  };
};