import React, { createContext, useContext, useState, useCallback } from 'react';
import { PanelConfig, PanelContextType, PanelPosition } from '../types/PanelTypes';
import { generatePanelIndexForMarket, generateTaiwanStockPanelIndex, parseIndexCode, generateDisplayCode, getCategoryPrefix } from '../utils/indexUtils';

const PanelContext = createContext<PanelContextType | undefined>(undefined);

export const usePanelContext = () => {
  const context = useContext(PanelContext);
  if (!context) {
    throw new Error('usePanelContext must be used within a PanelProvider');
  }
  return context;
};

// 預設台股面板配置
const getDefaultTaiwanPanels = (): Record<string, PanelConfig> => {
  const positions: PanelPosition[] = ['topLeft', 'topRight', 'bottomLeft', 'bottomRight'];
  const components = ['StockChart', 'MarketData', 'StockList', 'TechnicalAnalysis'];
  const titles = ['台股上市19類', '台股市場數據', '台股主圖表', '台股技術分析'];
  
  const panels: Record<string, PanelConfig> = {};
  
  positions.forEach((position, index) => {
    // 為台股面板生成帶有類股分類的索引碼
    let indexConfig;
    if (position === 'topLeft') {
      // 左上角面板顯示上市19類 - A1-上市1
      indexConfig = generateTaiwanStockPanelIndex('listed', position);
    } else if (position === 'bottomLeft') {
      // 左下角面板顯示主圖表 - A1-上市2
      indexConfig = generateTaiwanStockPanelIndex('listed', position);
    } else {
      // 其他面板使用通用台股索引
      indexConfig = generatePanelIndexForMarket('taiwan', position, '上市類股');
    }
    
    const panelId = `tw-${position.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
    
    panels[panelId] = {
      id: panelId,
      title: titles[index],
      component: components[index],
      position,
      size: position.includes('Left') ? { width: 60, height: position.includes('top') ? 60 : 40 } : { width: 40, height: position.includes('top') ? 60 : 40 },
      isVisible: true,
      isResizable: true,
      isDraggable: true,
      zIndex: 1,
      index: indexConfig.index,
      displayCode: indexConfig.displayCode
    };
  });
  
  return panels;
};

// 預設美股面板配置
const getDefaultUSPanels = (): Record<string, PanelConfig> => {
  const positions: PanelPosition[] = ['topLeft', 'topRight', 'bottomLeft', 'bottomRight'];
  const components = ['USStockChart', 'USMarketData', 'USStockList', 'USTechnicalAnalysis'];
  const titles = ['美股主圖表', '美股市場數據', '美股板塊列表', '美股技術分析'];
  
  const panels: Record<string, PanelConfig> = {};
  
  positions.forEach((position, index) => {
    const indexConfig = generatePanelIndexForMarket('us', position);
    const panelId = `us-${position.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
    
    panels[panelId] = {
      id: panelId,
      title: titles[index],
      component: components[index],
      position,
      size: position.includes('Left') ? { width: 60, height: position.includes('top') ? 60 : 40 } : { width: 40, height: position.includes('top') ? 60 : 40 },
      isVisible: true,
      isResizable: true,
      isDraggable: true,
      zIndex: 1,
      index: indexConfig.index,
      displayCode: indexConfig.displayCode
    };
  });
  
  return panels;
};

interface PanelProviderProps {
  children: React.ReactNode;
  market: 'taiwan' | 'us';
}

export const PanelProvider: React.FC<PanelProviderProps> = ({ children, market }) => {
  const [panels, setPanels] = useState<Record<string, PanelConfig>>(() => 
    market === 'taiwan' ? getDefaultTaiwanPanels() : getDefaultUSPanels()
  );

  // 更新面板配置
  const updatePanel = useCallback((id: string, updates: Partial<PanelConfig>) => {
    setPanels(prev => ({
      ...prev,
      [id]: {
        ...prev[id],
        ...updates
      }
    }));
  }, []);

  // 根據ID獲取面板
  const getPanelById = useCallback((id: string) => {
    return panels[id];
  }, [panels]);

  // 根據位置獲取面板
  const getPanelsByPosition = useCallback((position: PanelPosition) => {
    return Object.values(panels).filter(panel => panel.position === position);
  }, [panels]);

  // 根據索引碼獲取面板
  const getPanelByIndexCode = useCallback((indexCode: string) => {
    return Object.values(panels).find(panel => panel.displayCode === indexCode);
  }, [panels]);

  // 更新面板索引
  const updatePanelIndex = useCallback((id: string, indexCode: string) => {
    const parsedIndex = parseIndexCode(indexCode);
    if (parsedIndex) {
      updatePanel(id, {
        index: parsedIndex,
        displayCode: generateDisplayCode(parsedIndex)
      });
    }
  }, [updatePanel]);

  // 更新台股類股面板索引 - 只更新左上角面板(A1-上市1)的類股類型
  const updateTaiwanCategoryPanel = useCallback((categoryType: 'listed' | 'otc' | 'futures') => {
    const topLeftPanel = getPanelById('tw-top-left');
    if (topLeftPanel) {
      const newIndexConfig = generateTaiwanStockPanelIndex(categoryType, 'topLeft');
      updatePanel('tw-top-left', {
        index: newIndexConfig.index,
        displayCode: newIndexConfig.displayCode
      });
    }
  }, [getPanelById, updatePanel]);

  // 交換兩個面板的位置
  const swapPanels = useCallback((id1: string, id2: string) => {
    setPanels(prev => {
      const panel1 = prev[id1];
      const panel2 = prev[id2];
      
      if (!panel1 || !panel2) return prev;

      return {
        ...prev,
        [id1]: { ...panel1, position: panel2.position },
        [id2]: { ...panel2, position: panel1.position }
      };
    });
  }, []);

  // 重置面板配置
  const resetPanels = useCallback(() => {
    setPanels(market === 'taiwan' ? getDefaultTaiwanPanels() : getDefaultUSPanels());
  }, [market]);

  // 當市場切換時更新面板
  React.useEffect(() => {
    setPanels(market === 'taiwan' ? getDefaultTaiwanPanels() : getDefaultUSPanels());
  }, [market]);

  const value: PanelContextType = {
    panels,
    updatePanel,
    getPanelById,
    getPanelsByPosition,
    swapPanels,
    resetPanels,
    getPanelByIndexCode,
    updatePanelIndex,
    updateTaiwanCategoryPanel
  };

  return (
    <PanelContext.Provider value={value}>
      {children}
    </PanelContext.Provider>
  );
};