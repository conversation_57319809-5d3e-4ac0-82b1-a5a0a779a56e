import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react';

interface Stock {
  code: string;
  name: string;
  price: string;
  change: string;
  changePercent: string;
  volume: string;
  totalVolume: string;
  high: string;
  low: string;
  open: string;
}

interface Category {
  code: string;
  name: string;
  price: string;
  change: string;
  changePercent: string;
  volume: string;
  volumePercent: string;
}

interface StockListProps {
  selectedCategory?: {
    code: string;
    name: string;
  };
  onCategorySelect?: (categoryCode: string, categoryName: string) => void;
  onStockSelect?: (stockCode: string, stockName: string) => void;
}

const StockList: React.FC<StockListProps> = ({ selectedCategory, onCategorySelect, onStockSelect }) => {
  // 欄位寬度狀態 (百分比)
  const [columnWidths, setColumnWidths] = useState({
    nameCode: 20,      // 股票名稱/代號 或 商品名稱
    price: 10,         // 股價 或 成交價
    change: 8,         // 漲跌
    changePercent: 10, // 漲跌幅(%)
    open: 8,           // 開盤 或 成交值
    yesterday: 8,      // 昨收 或 成交比重%
    high: 8,           // 最高
    low: 8,            // 最低
    volume: 12,        // 成交量(張)
    time: 8            // 時間
  });

  const [isDragging, setIsDragging] = useState<string | null>(null);
  const [selectedCategoryCode, setSelectedCategoryCode] = useState<string | null>(null);
  const [selectedStockCode, setSelectedStockCode] = useState<string | null>(null); // 新增：選中的個股代碼
  const tableRef = useRef<HTMLTableElement>(null);

  // 上市19類股數據 - 當顯示類股列表時使用
  const listedCategories = useMemo(() => [
    { code: 'water', name: '水泥', price: '137.41c', change: '▲0.92', changePercent: '+0.67', volume: '8.66', volumePercent: '0.2230' },
    { code: 'food', name: '食品', price: '2080.68c', change: '▲26.17', changePercent: '+1.27', volume: '44.20', volumePercent: '1.1390' },
    { code: 'plastic', name: '塑膠', price: '98.96c', change: '▲1.77', changePercent: '+1.82', volume: '29.22', volumePercent: '0.7529' },
    { code: 'textile', name: '紡織纖維', price: '527.76c', change: '▲5.41', changePercent: '+1.04', volume: '11.83', volumePercent: '0.3049' },
    { code: 'machinery', name: '電機機械', price: '375.75c', change: '▲2.73', changePercent: '+0.73', volume: '198.36', volumePercent: '5.1115' },
    { code: 'cable', name: '電器電纜', price: '84.50c', change: '▲0.71', changePercent: '+0.85', volume: '15.22', volumePercent: '0.3920' },
    { code: 'chemical', name: '化學生技醫療', price: '134.65c', change: '▲1.41', changePercent: '+1.06', volume: '45.13', volumePercent: '1.1630' },
    { code: 'glass', name: '玻璃陶瓷', price: '45.29c', change: '▲0.36', changePercent: '+0.80', volume: '1.67', volumePercent: '0.0429' },
    { code: 'paper', name: '造紙', price: '240.69c', change: '▲2.71', changePercent: '+1.14', volume: '1.45', volumePercent: '0.0374' },
    { code: 'steel', name: '鋼鐵', price: '117.53c', change: '▲1.27', changePercent: '+1.09', volume: '25.82', volumePercent: '0.6654' },
    { code: 'rubber', name: '橡膠', price: '221.31c', change: '▲4.39', changePercent: '+2.02', volume: '5.60', volumePercent: '0.1444' },
    { code: 'auto', name: '汽車', price: '330.96c', change: '▲5.34', changePercent: '+1.64', volume: '30.24', volumePercent: '0.7792' },
    { code: 'electronics', name: '電子', price: '1253.88c', change: '▲0.98', changePercent: '+0.08', volume: '2714.08', volumePercent: '69.9383' },
    { code: 'construction', name: '建材營造', price: '507.25c', change: '▲7.62', changePercent: '+1.53', volume: '23.14', volumePercent: '0.5962' },
    { code: 'shipping', name: '航運業', price: '193.16c', change: '▼0.27', changePercent: '-0.14', volume: '98.25', volumePercent: '2.5318' },
    { code: 'tourism', name: '觀光餐旅', price: '113.96c', change: '▲1.54', changePercent: '+1.37', volume: '4.93', volumePercent: '0.1269' },
    { code: 'finance', name: '金融保險', price: '2157.97c', change: '▲10.47', changePercent: '+0.49', volume: '228.56', volumePercent: '5.8895' },
    { code: 'retail', name: '貿易百貨', price: '258.72c', change: '▲1.05', changePercent: '+0.41', volume: '6.56', volumePercent: '0.1690' },
    { code: 'others', name: '其他', price: '292.50c', change: '▲1.97', changePercent: '+0.68', volume: '25.58', volumePercent: '0.6592' },
  ], []);

  // 上櫃類股數據
  const otcCategories = useMemo(() => [
    { code: 'otc_tech', name: '電子工業', price: '145.23c', change: '▲2.15', changePercent: '+1.50', volume: '125.45', volumePercent: '15.2340' },
    { code: 'otc_biotech', name: '生技醫療', price: '89.67c', change: '▲1.23', changePercent: '+1.39', volume: '45.67', volumePercent: '5.5430' },
    { code: 'otc_cultural', name: '文化創意', price: '67.89c', change: '▼0.45', changePercent: '-0.66', volume: '12.34', volumePercent: '1.4980' },
    { code: 'otc_green', name: '綠能環保', price: '123.45c', change: '▲3.21', changePercent: '+2.67', volume: '78.90', volumePercent: '9.5870' },
    { code: 'otc_digital', name: '數位雲端', price: '234.56c', change: '▲5.67', changePercent: '+2.48', volume: '156.78', volumePercent: '19.0450' },
    { code: 'otc_service', name: '居家生活', price: '98.76c', change: '▲0.98', changePercent: '+1.00', volume: '34.56', volumePercent: '4.1950' },
  ], []);

  // 股票期貨數據
  const futuresCategories = useMemo(() => [
    { code: 'futures_tw', name: '台指期', price: '21850', change: '▲125', changePercent: '+0.58', volume: '89,456', volumePercent: '45.2340' },
    { code: 'futures_elect', name: '電子期', price: '1245.5', change: '▲8.5', changePercent: '+0.69', volume: '23,567', volumePercent: '11.9230' },
    { code: 'futures_finance', name: '金融期', price: '2156.8', change: '▲12.3', changePercent: '+0.57', volume: '15,678', volumePercent: '7.9340' },
    { code: 'futures_mini', name: '小台指期', price: '21845', change: '▲120', changePercent: '+0.55', volume: '45,234', volumePercent: '22.8760' },
    { code: 'futures_stock', name: '個股期貨', price: '567.5', change: '▲4.5', changePercent: '+0.80', volume: '12,890', volumePercent: '6.5120' },
    { code: 'futures_commodity', name: '商品期貨', price: '1890.2', change: '▼15.8', changePercent: '-0.83', volume: '8,945', volumePercent: '4.5210' },
  ], []);

  // 模擬各類股的個股數據 - 使用 useMemo 避免重複創建
  const getStocksByCategory = useMemo(() => {
    const stocksData: Record<string, Stock[]> = {
      '水泥': [
        { code: '1101', name: '台泥', price: '25.80', change: '▲0.10', changePercent: '+0.39', volume: '16,398', totalVolume: '16,398', high: '26.25', low: '25.70', open: '25.75' },
        { code: '1102', name: '亞泥', price: '42.65', change: '▲0.40', changePercent: '+0.95', volume: '9,760', totalVolume: '9,760', high: '42.90', low: '42.20', open: '42.20' },
        { code: '1103', name: '嘉泥', price: '14.65', change: '▲0.20', changePercent: '+1.38', volume: '326', totalVolume: '326', high: '14.75', low: '14.45', open: '14.45' },
        { code: '1104', name: '環泥', price: '28.15', change: '▲0.25', changePercent: '+0.90', volume: '468', totalVolume: '468', high: '28.15', low: '27.70', open: '27.85' },
        { code: '1108', name: '幸福', price: '14.90', change: '▲0.05', changePercent: '+0.34', volume: '89', totalVolume: '89', high: '15.00', low: '14.90', open: '14.95' },
        { code: '1109', name: '信大', price: '16.85', change: '▼0.05', changePercent: '-0.30', volume: '66', totalVolume: '66', high: '17.00', low: '16.85', open: '16.95' },
        { code: '1110', name: '東泥', price: '19.70', change: '▲0.30', changePercent: '+1.55', volume: '160', totalVolume: '160', high: '19.70', low: '19.45', open: '19.45' },
        { code: '1101B', name: '台泥乙特', price: '48.30', change: '0.00', changePercent: '0.00', volume: '48', totalVolume: '48', high: '48.35', low: '48.15', open: '48.35' },
      ],
      '電子': [
        { code: '2330', name: '台積電', price: '567.00', change: '▲8.00', changePercent: '+1.43', volume: '45,678', totalVolume: '89,234', high: '572.00', low: '561.00', open: '565.00' },
        { code: '2317', name: '鴻海', price: '89.70', change: '▲1.20', changePercent: '+1.36', volume: '123,456', totalVolume: '234,567', high: '90.50', low: '88.90', open: '89.20' },
        { code: '2454', name: '聯發科', price: '734.00', change: '▲12.00', changePercent: '+1.66', volume: '23,456', totalVolume: '45,678', high: '742.00', low: '728.00', open: '730.00' },
        { code: '2382', name: '廣達', price: '123.50', change: '▲2.50', changePercent: '+2.07', volume: '34,567', totalVolume: '67,890', high: '125.00', low: '121.50', open: '122.00' },
        { code: '3711', name: '日月光投控', price: '98.40', change: '▲1.80', changePercent: '+1.86', volume: '56,789', totalVolume: '89,012', high: '99.20', low: '97.50', open: '98.00' },
        { code: '2308', name: '台達電', price: '289.50', change: '▲4.50', changePercent: '+1.58', volume: '12,345', totalVolume: '23,456', high: '292.00', low: '286.00', open: '287.50' },
        { code: '6505', name: '台塑化', price: '89.20', change: '▲1.70', changePercent: '+1.94', volume: '78,901', totalVolume: '123,456', high: '90.00', low: '88.50', open: '88.80' },
        { code: '2303', name: '聯電', price: '45.65', change: '▲0.85', changePercent: '+1.90', volume: '234,567', totalVolume: '456,789', high: '46.20', low: '45.20', open: '45.40' },
      ],
      '金融保險': [
        { code: '2881', name: '富邦金', price: '67.80', change: '▲0.90', changePercent: '+1.35', volume: '89,012', totalVolume: '156,789', high: '68.20', low: '67.20', open: '67.50' },
        { code: '2882', name: '國泰金', price: '45.25', change: '▲0.65', changePercent: '+1.46', volume: '67,890', totalVolume: '123,456', high: '45.80', low: '44.90', open: '45.10' },
        { code: '2886', name: '兆豐金', price: '34.55', change: '▲0.45', changePercent: '+1.32', volume: '45,678', totalVolume: '89,012', high: '34.90', low: '34.20', open: '34.30' },
        { code: '2891', name: '中信金', price: '23.40', change: '▲0.30', changePercent: '+1.30', volume: '123,456', totalVolume: '234,567', high: '23.65', low: '23.15', open: '23.25' },
        { code: '2892', name: '第一金', price: '26.75', change: '▲0.35', changePercent: '+1.33', volume: '56,789', totalVolume: '98,765', high: '27.00', low: '26.50', open: '26.60' },
        { code: '5880', name: '合庫金', price: '22.85', change: '▲0.25', changePercent: '+1.11', volume: '78,901', totalVolume: '145,678', high: '23.10', low: '22.70', open: '22.80' },
      ],
      '食品': [
        { code: '1201', name: '味全', price: '23.45', change: '▲0.35', changePercent: '+1.52', volume: '34,567', totalVolume: '67,890', high: '23.80', low: '23.20', open: '23.30' },
        { code: '1216', name: '統一', price: '67.80', change: '▲1.20', changePercent: '+1.80', volume: '56,789', totalVolume: '98,765', high: '68.50', low: '67.20', open: '67.50' },
        { code: '1229', name: '聯華', price: '45.60', change: '▲0.80', changePercent: '+1.78', volume: '23,456', totalVolume: '45,678', high: '46.20', low: '45.20', open: '45.40' },
        { code: '1234', name: '黑松', price: '34.25', change: '▲0.55', changePercent: '+1.63', volume: '12,345', totalVolume: '23,456', high: '34.80', low: '34.00', open: '34.15' },
      ],
      '航運業': [
        { code: '2603', name: '長榮', price: '156.50', change: '▼2.50', changePercent: '-1.57', volume: '234,567', totalVolume: '456,789', high: '159.50', low: '155.00', open: '158.00' },
        { code: '2609', name: '陽明', price: '67.20', change: '▼1.30', changePercent: '-1.90', volume: '123,456', totalVolume: '234,567', high: '69.00', low: '66.80', open: '68.20' },
        { code: '2615', name: '萬海', price: '89.40', change: '▼1.60', changePercent: '-1.76', volume: '89,012', totalVolume: '156,789', high: '91.50', low: '88.90', open: '90.20' },
        { code: '5608', name: '四維航', price: '12.85', change: '▼0.15', changePercent: '-1.15', volume: '45,678', totalVolume: '78,901', high: '13.10', low: '12.80', open: '12.95' },
      ],
      '塑膠': [
        { code: '1301', name: '台塑', price: '89.20', change: '▲1.70', changePercent: '+1.94', volume: '78,901', totalVolume: '123,456', high: '90.00', low: '88.50', open: '88.80' },
        { code: '1303', name: '南亞', price: '67.50', change: '▲1.20', changePercent: '+1.81', volume: '45,678', totalVolume: '89,012', high: '68.20', low: '66.80', open: '67.00' },
        { code: '1326', name: '台化', price: '78.90', change: '▲1.50', changePercent: '+1.94', volume: '34,567', totalVolume: '67,890', high: '79.50', low: '78.20', open: '78.40' },
        { code: '6505', name: '台塑化', price: '89.20', change: '▲1.70', changePercent: '+1.94', volume: '78,901', totalVolume: '123,456', high: '90.00', low: '88.50', open: '88.80' },
      ]
    };

    return (categoryName: string): Stock[] => {
      return stocksData[categoryName] || [];
    };
  }, []);

  // 判斷是否顯示類股列表（A1-上市1）還是個股列表（A1-上市2）
  const isShowingCategories = selectedCategory?.code === 'listed' || selectedCategory?.code === 'otc' || selectedCategory?.code === 'futures';
  
  // 獲取要顯示的數據
  const displayData = useMemo(() => {
    if (isShowingCategories) {
      // 根據選擇的類別顯示對應的類股列表
      switch (selectedCategory?.code) {
        case 'listed':
          return listedCategories;
        case 'otc':
          return otcCategories;
        case 'futures':
          return futuresCategories;
        default:
          return listedCategories;
      }
    } else if (selectedCategory) {
      return getStocksByCategory(selectedCategory.name);
    }
    return [];
  }, [isShowingCategories, listedCategories, otcCategories, futuresCategories, selectedCategory, getStocksByCategory]);

  // 處理類股點擊 - 只在顯示類股列表時有效
  const handleCategoryClick = useCallback((category: Category) => {
    if (isShowingCategories && onCategorySelect) {
      setSelectedCategoryCode(category.code);
      onCategorySelect(category.code, category.name);
    }
  }, [isShowingCategories, onCategorySelect]);

  // 新增：處理個股點擊 - 只在顯示個股列表時有效
  const handleStockClick = useCallback((stock: Stock) => {
    if (!isShowingCategories) {
      setSelectedStockCode(stock.code);
      // 通知父組件個股被選中
      if (onStockSelect) {
        onStockSelect(stock.code, stock.name);
      }
      console.log('選中個股:', stock.name, stock.code);
    }
  }, [isShowingCategories, onStockSelect]);



  // 自動選擇第一項
  useEffect(() => {
    if (isShowingCategories && displayData.length > 0 && !selectedCategoryCode) {
      // 如果顯示類股列表且沒有選中任何類股，自動選擇第一項
      const firstCategory = displayData[0] as Category;
      setSelectedCategoryCode(firstCategory.code);
      if (onCategorySelect) {
        onCategorySelect(firstCategory.code, firstCategory.name);
      }
    } else if (!isShowingCategories && displayData.length > 0 && !selectedStockCode) {
      // 如果顯示個股列表且沒有選中任何個股，自動選擇第一項
      const firstStock = displayData[0] as Stock;
      setSelectedStockCode(firstStock.code);
      if (onStockSelect) {
        onStockSelect(firstStock.code, firstStock.name);
      }
    }
  }, [isShowingCategories, displayData, selectedCategoryCode, selectedStockCode, onCategorySelect, onStockSelect]);

  // 處理欄位調整開始
  const handleResizeStart = useCallback((columnKey: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(columnKey);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, []);

  // 處理欄位調整
  const handleResize = useCallback((e: MouseEvent) => {
    if (!isDragging || !tableRef.current) return;

    const tableRect = tableRef.current.getBoundingClientRect();
    const mouseX = e.clientX - tableRect.left;
    const tableWidth = tableRect.width;
    const mousePercent = (mouseX / tableWidth) * 100;

    setColumnWidths(prev => {
      const newWidths = { ...prev };
      const columnKeys = Object.keys(newWidths);
      const currentIndex = columnKeys.indexOf(isDragging);
      
      if (currentIndex === -1) return prev;

      // 計算當前欄位之前所有欄位的總寬度
      let totalPrevWidth = 0;
      for (let i = 0; i < currentIndex; i++) {
        totalPrevWidth += newWidths[columnKeys[i] as keyof typeof newWidths];
      }

      // 計算新的欄位寬度
      const newWidth = Math.max(5, Math.min(40, mousePercent - totalPrevWidth));
      const oldWidth = newWidths[isDragging as keyof typeof newWidths];
      const widthDiff = newWidth - oldWidth;

      // 更新當前欄位寬度
      newWidths[isDragging as keyof typeof newWidths] = newWidth;

      // 調整後續欄位寬度以保持總寬度為100%
      if (currentIndex < columnKeys.length - 1) {
        const remainingColumns = columnKeys.slice(currentIndex + 1);
        const totalRemainingWidth = remainingColumns.reduce((sum, col) => 
          sum + newWidths[col as keyof typeof newWidths], 0
        );

        if (totalRemainingWidth > 0) {
          remainingColumns.forEach(col => {
            const currentColWidth = newWidths[col as keyof typeof newWidths];
            const ratio = currentColWidth / totalRemainingWidth;
            newWidths[col as keyof typeof newWidths] = Math.max(5, currentColWidth - (widthDiff * ratio));
          });
        }
      }

      return newWidths;
    });
  }, [isDragging]);

  // 處理欄位調整結束
  const handleResizeEnd = useCallback(() => {
    setIsDragging(null);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, []);

  // 監聽滑鼠事件
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleResize);
      document.addEventListener('mouseup', handleResizeEnd);

      return () => {
        document.removeEventListener('mousemove', handleResize);
        document.removeEventListener('mouseup', handleResizeEnd);
      };
    }
  }, [isDragging, handleResize, handleResizeEnd]);

  // 如果沒有數據，顯示預設的櫃買指數圖表
  if (!selectedCategory || displayData.length === 0) {
    // 模擬股價數據
    const generateChartData = () => {
      const data = [];
      let price = 233.17;
      for (let i = 0; i < 100; i++) {
        price += (Math.random() - 0.5) * 2;
        data.push(price);
      }
      return data;
    };

    const chartData = generateChartData();
    const currentPrice = 233.17;
    const change = 1.50;
    const changePercent = 0.50;

    return (
      <div className="h-full bg-black rounded border border-gray-700 p-4">
        {/* Chart Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <span className="text-white font-bold">櫃買指數(OTC)</span>
            <span className="text-white">13:35:00</span>
            <span className="text-green-400 font-bold text-lg">{currentPrice}</span>
            <span className="text-green-400">▲{change} +{changePercent}%</span>
            <span className="text-gray-400">1142.93億</span>
          </div>
        </div>

        {/* Chart Area */}
        <div className="relative h-64 mb-4">
          <svg className="w-full h-full">
            {/* Grid Lines */}
            {[0, 1, 2, 3, 4].map(i => (
              <line
                key={i}
                x1="0"
                y1={i * 64}
                x2="100%"
                y2={i * 64}
                stroke="#374151"
                strokeWidth="1"
              />
            ))}
            
            {/* Price Line */}
            <polyline
              fill="none"
              stroke="#10B981"
              strokeWidth="1"
              points={chartData.map((price, index) => 
                `${(index / chartData.length) * 100}%,${((235 - price) / 5) * 100}%`
              ).join(' ')}
            />
          </svg>
          
          {/* Price Labels */}
          <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-green-400 text-xs">
            <span>234.3</span>
            <span>233.5</span>
            <span>232.8</span>
            <span>232.1</span>
            <span>231.5</span>
            <span>230.7</span>
            <span>230.1</span>
          </div>
        </div>

        {/* Volume Chart */}
        <div className="h-20 relative">
          <div className="flex items-end h-full space-x-1">
            {Array.from({length: 50}, (_, i) => (
              <div
                key={i}
                className="bg-purple-500 flex-1"
                style={{height: `${Math.random() * 100}%`}}
              />
            ))}
          </div>
          <div className="absolute left-0 top-0 text-white text-xs">
            <div>40.00</div>
            <div className="mt-2">20.00</div>
            <div className="mt-2">0</div>
          </div>
        </div>

        {/* Time Labels */}
        <div className="flex justify-between text-gray-400 text-xs mt-2">
          <span>9</span>
          <span>10</span>
          <span>11</span>
          <span>12</span>
          <span>13</span>
          <span>13:30</span>
        </div>
      </div>
    );
  }

  // 渲染類股行或個股行
  const renderTableRow = (item: Category | Stock, index: number) => {
    if (isShowingCategories) {
      // 渲染類股行
      const category = item as Category;
      const isSelected = selectedCategoryCode === category.code;
      
      return (
        <tr 
          key={index} 
          className="hover:bg-gray-800 border-b border-gray-800 cursor-pointer transition-colors duration-200"
          onClick={() => handleCategoryClick(category)}
        >
          <td className="px-2 py-1 border-r border-gray-700 truncate" style={{ width: `${columnWidths.nameCode}%` }}>
            <span className="text-white font-medium">
              {/* 顯示雙三角形選中指示符 */}
              {isSelected && <span className="text-blue-400 mr-1">▶▶</span>}
              {category.name}
            </span>
          </td>
          <td className={`px-2 py-1 text-right border-r border-gray-700 font-medium truncate ${
            category.change.includes('▲') ? 'text-red-400' : 
            category.change.includes('▼') ? 'text-green-400' : 'text-white'
          }`} style={{ width: `${columnWidths.price}%` }}>
            {category.price}
          </td>
          <td className={`px-2 py-1 text-right border-r border-gray-700 truncate ${
            category.change.includes('▲') ? 'text-red-400' : 
            category.change.includes('▼') ? 'text-green-400' : 'text-white'
          }`} style={{ width: `${columnWidths.change}%` }}>
            {category.change}
          </td>
          <td className={`px-2 py-1 text-right border-r border-gray-700 truncate ${
            category.changePercent.includes('+') ? 'text-red-400' : 
            category.changePercent.includes('-') ? 'text-green-400' : 'text-white'
          }`} style={{ width: `${columnWidths.changePercent}%` }}>
            {category.changePercent}
          </td>
          <td className="px-2 py-1 text-white text-right border-r border-gray-700 truncate" style={{ width: `${columnWidths.open}%` }}>
            {category.volume}
          </td>
          <td className="px-2 py-1 text-white text-right truncate" style={{ width: `${columnWidths.yesterday}%` }}>
            {category.volumePercent}
          </td>
        </tr>
      );
    } else {
      // 渲染個股行
      const stock = item as Stock;
      const yesterdayPrice = (parseFloat(stock.price) - parseFloat(stock.change.replace(/[▲▼]/g, ''))).toFixed(2);
      const isStockSelected = selectedStockCode === stock.code; // 檢查個股是否被選中
      
      return (
        <tr 
          key={index} 
          className="hover:bg-gray-800 border-b border-gray-800 cursor-pointer transition-colors duration-200"
          onClick={() => handleStockClick(stock)} // 添加個股點擊處理
        >
          <td className="px-2 py-1 border-r border-gray-700 truncate" style={{ width: `${columnWidths.nameCode}%` }}>
            <span className="text-white font-medium">
              {/* 顯示個股雙三角形選中指示符 */}
              {isStockSelected && <span className="text-blue-400 mr-1">▶▶</span>}
              {stock.name}
            </span>
          </td>
          <td className={`px-2 py-1 text-right border-r border-gray-700 font-medium truncate ${
            stock.change.includes('▲') ? 'text-red-400' :
            stock.change.includes('▼') ? 'text-green-400' : 'text-white'
          }`} style={{ width: `${columnWidths.price}%` }}>
            {stock.price}
          </td>
          <td className={`px-2 py-1 text-right border-r border-gray-700 truncate ${
            stock.change.includes('▲') ? 'text-red-400' :
            stock.change.includes('▼') ? 'text-green-400' : 'text-white'
          }`} style={{ width: `${columnWidths.change}%` }}>
            {stock.change}
          </td>
          <td className={`px-2 py-1 text-right border-r border-gray-700 truncate ${
            stock.changePercent.includes('+') ? 'text-red-400' :
            stock.changePercent.includes('-') ? 'text-green-400' : 'text-white'
          }`} style={{ width: `${columnWidths.changePercent}%` }}>
            {stock.changePercent}
          </td>
          <td className="px-2 py-1 text-white text-right border-r border-gray-700 truncate" style={{ width: `${columnWidths.open}%` }}>
            {stock.volume}
          </td>
          <td className="px-2 py-1 text-white text-right truncate" style={{ width: `${columnWidths.yesterday}%` }}>
            {stock.volumePercent || '0.0000'}
          </td>
        </tr>
      );
    }
  };

  // 渲染表格標題
  const renderTableHeader = () => {
    if (isShowingCategories) {
      // 類股表格標題
      return (
        <tr className="text-gray-300">
          <th className="px-2 py-1 text-left border-r border-gray-700 relative group" style={{ width: `${columnWidths.nameCode}%` }}>
            商品
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('nameCode', e)} />
          </th>
          <th className="px-2 py-1 text-right border-r border-gray-700 relative group" style={{ width: `${columnWidths.price}%` }}>
            成交
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('price', e)} />
          </th>
          <th className="px-2 py-1 text-right border-r border-gray-700 relative group" style={{ width: `${columnWidths.change}%` }}>
            漲跌
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('change', e)} />
          </th>
          <th className="px-2 py-1 text-right border-r border-gray-700 relative group" style={{ width: `${columnWidths.changePercent}%` }}>
            漲跌%
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('changePercent', e)} />
          </th>
          <th className="px-2 py-1 text-right border-r border-gray-700 relative group" style={{ width: `${columnWidths.open}%` }}>
            成交值
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('open', e)} />
          </th>
          <th className="px-2 py-1 text-right" style={{ width: `${columnWidths.yesterday}%` }}>
            成交比重%
          </th>
        </tr>
      );
    } else {
      // 個股表格標題
      return (
        <tr className="text-gray-300">
          <th className="px-2 py-1 text-left border-r border-gray-700 relative group" style={{ width: `${columnWidths.nameCode}%` }}>
            股票名稱/代號
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('nameCode', e)} />
          </th>
          <th className="px-2 py-1 text-right border-r border-gray-700 relative group" style={{ width: `${columnWidths.price}%` }}>
            股價
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('price', e)} />
          </th>
          <th className="px-2 py-1 text-right border-r border-gray-700 relative group" style={{ width: `${columnWidths.change}%` }}>
            漲跌
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('change', e)} />
          </th>
          <th className="px-2 py-1 text-right border-r border-gray-700 relative group" style={{ width: `${columnWidths.changePercent}%` }}>
            漲跌幅(%)
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('changePercent', e)} />
          </th>
          <th className="px-2 py-1 text-right border-r border-gray-700 relative group" style={{ width: `${columnWidths.open}%` }}>
            開盤
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('open', e)} />
          </th>
          <th className="px-2 py-1 text-right border-r border-gray-700 relative group" style={{ width: `${columnWidths.yesterday}%` }}>
            昨收
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('yesterday', e)} />
          </th>
          <th className="px-2 py-1 text-right border-r border-gray-700 relative group" style={{ width: `${columnWidths.high}%` }}>
            最高
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('high', e)} />
          </th>
          <th className="px-2 py-1 text-right border-r border-gray-700 relative group" style={{ width: `${columnWidths.low}%` }}>
            最低
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('low', e)} />
          </th>
          <th className="px-2 py-1 text-right border-r border-gray-700 relative group" style={{ width: `${columnWidths.volume}%` }}>
            成交量(張)
            <div className="absolute top-0 right-0 w-0.5 h-full cursor-col-resize bg-transparent hover:bg-cyan-400 transition-colors z-10" onMouseDown={(e) => handleResizeStart('volume', e)} />
          </th>
          <th className="px-2 py-1 text-right" style={{ width: `${columnWidths.time}%` }}>
            時間
          </th>
        </tr>
      );
    }
  };

  // 顯示標題
  const getTitle = () => {
    if (isShowingCategories) {
      switch (selectedCategory?.code) {
        case 'listed':
          return `上市19類 (共${displayData.length}筆結果)`;
        case 'otc':
          return `上櫃類股 (共${displayData.length}筆結果)`;
        case 'futures':
          return `股票期貨 (共${displayData.length}筆結果)`;
        default:
          return `類股列表 (共${displayData.length}筆結果)`;
      }
    } else {
      return `${selectedCategory?.name}分類行情 (共${displayData.length}筆結果)`;
    }
  };

  return (
    <div className="h-full bg-gray-900 relative flex flex-col">
      <div className="bg-gray-800 px-3 py-2 border-b border-gray-700 flex-shrink-0">
        <h3 className="text-white text-sm font-semibold">
          {getTitle()}
        </h3>
      </div>
      <div className="flex-1 table-container custom-scrollbar">
        <table ref={tableRef} className="w-full text-xs table-fixed">
          <thead className="bg-gray-800 sticky top-0 z-10">
            {renderTableHeader()}
          </thead>
          <tbody>
            {displayData.map((item, index) => renderTableRow(item, index))}
          </tbody>
        </table>
      </div>

      {/* 拖拽時的覆蓋層 */}
      {isDragging && (
        <div className="absolute inset-0 z-30 bg-transparent" />
      )}
    </div>
  );
};

export default StockList;