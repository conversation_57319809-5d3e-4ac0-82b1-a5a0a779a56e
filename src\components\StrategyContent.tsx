import React, { useState, useRef, useEffect } from 'react';
import { Edit, Trash2, Play, Square, BarChart3, Clock, XCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels';

// 策略項目定義
interface StrategyItem {
  id: number;
  enabled: boolean;
  autoExecute: boolean;
  executionStatus: '停止' | '啟動' | '自動執行' | '解除自動';
  strategyCode: string;
  strategyName: string;
  tradingProduct: string;
  direction: '多頭' | '空頭' | '雙向';
  frequency: string;
  activationTime: string;
}

// 初始策略數據
const initialStrategies = {
  postMarket: [
    { id: 1, enabled: true, autoExecute: false, executionStatus: '停止' as const, strategyCode: 'PMS001', strategyName: '均線突破選股', tradingProduct: '台股', direction: '多頭' as const, frequency: '每日', activationTime: '09:00' },
    { id: 2, enabled: false, autoExecute: true, executionStatus: '自動執行' as const, strategyCode: 'PMS002', strategyName: 'RSI超賣選股', tradingProduct: '台股', direction: '多頭' as const, frequency: '每日', activationTime: '09:30' },
    { id: 3, enabled: true, autoExecute: false, executionStatus: '停止' as const, strategyCode: 'PMS003', strategyName: '布林通道突破', tradingProduct: '台股', direction: '雙向' as const, frequency: '每週', activationTime: '10:00' }
  ],
  alert: [
    { id: 4, enabled: true, autoExecute: false, executionStatus: '停止' as const, strategyCode: 'ALT001', strategyName: '價格突破警示', tradingProduct: '台股', direction: '雙向' as const, frequency: '即時', activationTime: '09:00' },
    { id: 5, enabled: false, autoExecute: true, executionStatus: '自動執行' as const, strategyCode: 'ALT002', strategyName: 'RSI超買超賣警示', tradingProduct: '台股', direction: '雙向' as const, frequency: '即時', activationTime: '09:00' },
    { id: 6, enabled: true, autoExecute: false, executionStatus: '停止' as const, strategyCode: 'ALT003', strategyName: '成交量異常警示', tradingProduct: '台股', direction: '雙向' as const, frequency: '即時', activationTime: '09:00' }
  ],
  trading: [
    { id: 7, enabled: true, autoExecute: false, executionStatus: '啟動' as const, strategyCode: 'TRD001', strategyName: '動量交易策略', tradingProduct: '台股', direction: '多頭' as const, frequency: '即時', activationTime: '09:00' },
    { id: 8, enabled: false, autoExecute: true, executionStatus: '自動執行' as const, strategyCode: 'TRD002', strategyName: '均值回歸策略', tradingProduct: '台股', direction: '空頭' as const, frequency: '每小時', activationTime: '09:30' },
    { id: 9, enabled: true, autoExecute: false, executionStatus: '停止' as const, strategyCode: 'TRD003', strategyName: '網格交易策略', tradingProduct: '台股', direction: '雙向' as const, frequency: '即時', activationTime: '10:00' }
  ]
};

interface StrategyContentProps {
  selectedCategory: string;
}

const StrategyContent: React.FC<StrategyContentProps> = ({ selectedCategory }) => {
  const [postMarketStrategies, setPostMarketStrategies] = useState<StrategyItem[]>(initialStrategies.postMarket);
  const [alertStrategies, setAlertStrategies] = useState<StrategyItem[]>(initialStrategies.alert);
  const [tradingStrategies, setTradingStrategies] = useState<StrategyItem[]>(initialStrategies.trading);

  // 策略篩選結果相關狀態
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedStocks, setSelectedStocks] = useState<number[]>([]);
  const [showColumnSelector, setShowColumnSelector] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState({
    序號: true,
    代碼: true,
    商品: true,
    成交: true,
    漲跌: true,
    總量: true,
    月營收月增率: true,
    月營收年增率: true,
  });

  // 自選股股池相關狀態
  const [selectedPool, setSelectedPool] = useState('日內當沖');
  const [selectedPoolStocks, setSelectedPoolStocks] = useState<number[]>([]);
  const [showAddStockModal, setShowAddStockModal] = useState(false);
  const [stockTypeToAdd, setStockTypeToAdd] = useState('');

  // 選中的盤後選股策略
  const [selectedPostMarketStrategy, setSelectedPostMarketStrategy] = useState<number | null>(null);

  // 模擬股票數據
  const [stockData, setStockData] = useState([
    { id: 1, code: '1268', name: '漢來美食', price: 152.5, change: 0.33, volume: 5, monthlyGrowth: 31.38, yearlyGrowth: 21.44 },
    { id: 2, code: '1315', name: '達新', price: 67, change: 0.6, volume: 5, monthlyGrowth: 47.42, yearlyGrowth: 22 },
    { id: 3, code: '1436', name: '華友聯', price: 121, change: 0.41, volume: 227, monthlyGrowth: 1457.4, yearlyGrowth: 53.58 },
    { id: 4, code: '1439', name: '中和', price: 30.35, change: 0.66, volume: 5, monthlyGrowth: 313, yearlyGrowth: 115.69 },
    { id: 5, code: '1467', name: '南緯', price: 8.19, change: -1.09, volume: 46, monthlyGrowth: 29.33, yearlyGrowth: 34.12 },
    { id: 6, code: '1514', name: '亞力', price: 107, change: -4.04, volume: 2953, monthlyGrowth: 88.76, yearlyGrowth: 60.42 },
    { id: 7, code: '1530', name: '亞崴', price: 28.45, change: -3.07, volume: 74, monthlyGrowth: 20.45, yearlyGrowth: 29.1 },
    { id: 8, code: '1535', name: '中宇', price: 56.1, change: -0.18, volume: 21, monthlyGrowth: 30.87, yearlyGrowth: 28.14 },
  ]);

  // 自選股池數據
  const [stockPools, setStockPools] = useState({
    日內當沖: [
      { id: 1, code: '2330', name: '台積電', type: '日內當沖', price: 580, change: 2.15, volume: 25847, addTime: '2025/01/15', note: '高流動性' },
      { id: 2, code: '1301', name: '台塑', type: '日內當沖', price: 87.2, change: -0.68, volume: 18654, addTime: '2025/01/11', note: '波動大' },
    ],
    日波段: [],
    週波段: [],
    月波段: [],
    全部: []
  });

  // 日期處理函數
  const formatDate = (date: Date) => {
    return `${date.getFullYear()}年 ${date.getMonth() + 1}月 ${date.getDate()}日`;
  };

  const changeDate = (days: number) => {
    const newDate = new Date(currentDate);
    newDate.setDate(newDate.getDate() + days);
    setCurrentDate(newDate);
  };

  // 股票選擇處理
  const handleStockSelect = (stockId: number) => {
    setSelectedStocks(prev =>
      prev.includes(stockId)
        ? prev.filter(id => id !== stockId)
        : [...prev, stockId]
    );
  };

  const handleSelectAll = () => {
    if (selectedStocks.length === stockData.length) {
      setSelectedStocks([]);
    } else {
      setSelectedStocks(stockData.map(stock => stock.id));
    }
  };

  // 策略點擊處理
  const handleStrategyClick = (strategyId: number, action: string) => {
    console.log(`策略 ${strategyId} 執行操作: ${action}`);

    switch (action) {
      case 'start':
        setSelectedPostMarketStrategy(strategyId);
        // 更新策略的執行狀態為啟動
        setPostMarketStrategies(prev =>
          prev.map(s => s.id === strategyId ? { ...s, executionStatus: '啟動', enabled: true } : s)
        );
        console.log(`啟動策略 ${strategyId}，開始篩選股票`);
        // 模擬策略篩選結果更新
        updateStockDataForStrategy(strategyId);
        break;

      case 'stop':
        // 更新策略的執行狀態為停止
        setPostMarketStrategies(prev =>
          prev.map(s => s.id === strategyId ? { ...s, executionStatus: '停止', enabled: false, autoExecute: false } : s)
        );
        if (selectedPostMarketStrategy === strategyId) {
          setSelectedPostMarketStrategy(null);
          // 停止策略時恢復原始股票數據
          resetStockData();
        }
        console.log(`停止策略 ${strategyId}`);
        break;

      case 'backtest':
        console.log(`執行策略 ${strategyId} 的回測`);
        // 執行回測時也更新股票數據顯示回測結果
        updateStockDataForStrategy(strategyId, true);
        break;

      case 'daily-auto':
        setSelectedPostMarketStrategy(strategyId);
        // 更新策略的執行狀態為自動執行
        setPostMarketStrategies(prev =>
          prev.map(s => s.id === strategyId ? { ...s, executionStatus: '自動執行', autoExecute: true, enabled: true } : s)
        );
        console.log(`設定策略 ${strategyId} 為每日自動執行`);
        // 設置自動執行時也啟動策略
        updateStockDataForStrategy(strategyId);
        break;

      case 'cancel-auto':
        // 更新策略的執行狀態為解除自動
        setPostMarketStrategies(prev =>
          prev.map(s => s.id === strategyId ? { ...s, executionStatus: '解除自動', autoExecute: false, enabled: false } : s)
        );
        // 如果取消的是當前選中的策略，則停止策略並清空結果
        if (selectedPostMarketStrategy === strategyId) {
          setSelectedPostMarketStrategy(null);
          resetStockData();
        }
        console.log(`取消策略 ${strategyId} 的自動執行`);
        break;

      default:
        console.log(`未知操作: ${action}`);
    }
  };

  // 原始股票數據備份
  const originalStockData = [
    { id: 1, code: '1268', name: '漢來美食', price: 152.5, change: 0.33, volume: 5, monthlyGrowth: 31.38, yearlyGrowth: 21.44 },
    { id: 2, code: '1315', name: '達新', price: 67, change: 0.6, volume: 5, monthlyGrowth: 47.42, yearlyGrowth: 22 },
    { id: 3, code: '1436', name: '華友聯', price: 121, change: 0.41, volume: 227, monthlyGrowth: 1457.4, yearlyGrowth: 53.58 },
    { id: 4, code: '1439', name: '中和', price: 30.35, change: 0.66, volume: 5, monthlyGrowth: 313, yearlyGrowth: 115.69 },
    { id: 5, code: '1467', name: '南緯', price: 8.19, change: -1.09, volume: 46, monthlyGrowth: 29.33, yearlyGrowth: 34.12 },
    { id: 6, code: '1514', name: '亞力', price: 107, change: -4.04, volume: 2953, monthlyGrowth: 88.76, yearlyGrowth: 60.42 },
    { id: 7, code: '1530', name: '亞崴', price: 28.45, change: -3.07, volume: 74, monthlyGrowth: 20.45, yearlyGrowth: 29.1 },
    { id: 8, code: '1535', name: '中宇', price: 56.1, change: -0.18, volume: 21, monthlyGrowth: 30.87, yearlyGrowth: 28.14 },
  ];

  // 根據策略更新股票數據
  const updateStockDataForStrategy = (strategyId: number, isBacktest: boolean = false) => {
    console.log(`更新策略 ${strategyId} 的篩選結果${isBacktest ? '(回測)' : ''}`);

    // 根據不同策略ID顯示不同的篩選結果
    let filteredData = [];

    switch (strategyId) {
      case 1: // 均線突破選股
        filteredData = originalStockData.filter(stock => stock.change > 0);
        break;
      case 2: // RSI超賣選股
        filteredData = originalStockData.filter(stock => stock.monthlyGrowth > 50);
        break;
      case 3: // 布林通道突破
        filteredData = originalStockData.filter(stock => stock.volume > 100);
        break;
      default:
        filteredData = originalStockData;
    }

    if (isBacktest) {
      // 回測時添加標識
      filteredData = filteredData.map(stock => ({
        ...stock,
        name: stock.name + '(回測)'
      }));
    }

    setStockData(filteredData);
    console.log(`策略 ${strategyId} 篩選出 ${filteredData.length} 支股票`);
  };

  // 重置股票數據
  const resetStockData = () => {
    setStockData(originalStockData);
    console.log('已重置為原始股票數據');
  };

  // 獲取策略名稱
  const getStrategyName = (strategyId: number) => {
    const strategy = postMarketStrategies.find(s => s.id === strategyId);
    return strategy ? strategy.strategyName : `策略 ${strategyId}`;
  };

  // 添加到自選股
  const handleAddToPool = (type: string) => {
    if (selectedStocks.length === 0) return;
    setStockTypeToAdd(type);
    setShowAddStockModal(true);
  };

  // 獲取當前顯示的股票列表
  const getCurrentPoolStocks = () => {
    if (selectedPool === '全部') {
      // 合併所有股池的股票
      return [
        ...stockPools.日內當沖,
        ...stockPools.日波段,
        ...stockPools.週波段,
        ...stockPools.月波段
      ];
    }
    return stockPools[selectedPool as keyof typeof stockPools] || [];
  };

  // 根據選中的策略更新股票數據
  React.useEffect(() => {
    if (selectedPostMarketStrategy !== null) {
      console.log(`策略 ${selectedPostMarketStrategy} 已啟動，更新策略篩選結果`);
      // 這裡可以根據不同的策略ID顯示不同的股票結果
      // 目前使用模擬數據，實際應用中可以調用API獲取策略篩選結果
    }
  }, [selectedPostMarketStrategy]);

  // 策略表格組件
  const StrategyTable: React.FC<{
    title: string;
    strategies: StrategyItem[];
    type: 'postMarket' | 'alert' | 'trading';
    color: string;
  }> = ({ title, strategies, type, color }) => {
    // 根據類型獲取選中的策略
    const getSelectedStrategy = () => {
      if (type === 'postMarket') return selectedPostMarketStrategy;
      // 可以添加其他類型的選中策略
      return null;
    };

    const selectedStrategy = getSelectedStrategy();

    return (
      <div className="bg-gray-800 rounded-lg p-2 border border-gray-700 mb-2">
        <div className="flex items-center justify-between mb-2">
          <h2 className={cn("text-lg font-semibold", color)}>{title}</h2>
          <div className="flex items-center space-x-4">
            {/* 只有盤後選股策略顯示操作按鈕 */}
            {type === 'postMarket' && (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-green-600 text-green-400 hover:bg-green-600 hover:text-white",
                    !selectedStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedStrategy}
                  onClick={() => selectedStrategy && handleStrategyClick(selectedStrategy, 'start')}
                >
                  <Play className="h-3 w-3 mr-1" />
                  啟動
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-red-600 text-red-400 hover:bg-red-600 hover:text-white",
                    !selectedStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedStrategy}
                  onClick={() => selectedStrategy && handleStrategyClick(selectedStrategy, 'stop')}
                >
                  <Square className="h-3 w-3 mr-1" />
                  停止
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-blue-600 text-blue-400 hover:bg-blue-600 hover:text-white",
                    !selectedStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedStrategy}
                  onClick={() => selectedStrategy && handleStrategyClick(selectedStrategy, 'backtest')}
                >
                  <BarChart3 className="h-3 w-3 mr-1" />
                  回測
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-purple-600 text-purple-400 hover:bg-purple-600 hover:text-white",
                    !selectedStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedStrategy}
                  onClick={() => selectedStrategy && handleStrategyClick(selectedStrategy, 'daily-auto')}
                >
                  <Clock className="h-3 w-3 mr-1" />
                  每日自動執行
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-orange-600 text-orange-400 hover:bg-orange-600 hover:text-white",
                    !selectedStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedStrategy}
                  onClick={() => selectedStrategy && handleStrategyClick(selectedStrategy, 'cancel-auto')}
                >
                  <XCircle className="h-3 w-3 mr-1" />
                  解除自動執行
                </Button>
              </div>
            )}
            <Button
              variant="outline"
              size="sm"
              className="h-8 px-3 border-cyan-600 text-cyan-400 hover:bg-cyan-600 hover:text-white"
            >
              +策略
            </Button>
          </div>
        </div>

        <div className="rounded-md border border-gray-700">
          <Table>
            <TableHeader>
              <TableRow className="border-gray-600 hover:bg-gray-700/50">
                <TableHead className="text-gray-300">項數</TableHead>
                <TableHead className="text-gray-300">執行狀態</TableHead>
                <TableHead className="text-gray-300">策略編號</TableHead>
                <TableHead className="text-gray-300">策略名稱</TableHead>
                <TableHead className="text-gray-300">交易商品</TableHead>
                <TableHead className="text-gray-300">多空方向</TableHead>
                <TableHead className="text-gray-300">指定頻率</TableHead>
                <TableHead className="text-gray-300">啟動時間</TableHead>
                <TableHead className="text-gray-300">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {strategies.map((strategy, index) => (
                <TableRow
                  key={strategy.id}
                  className={cn(
                    "border-gray-600 hover:bg-gray-700/50 cursor-pointer",
                    type === 'postMarket' && selectedStrategy === strategy.id && "bg-blue-600/20 border-blue-500"
                  )}
                  onClick={() => {
                    if (type === 'postMarket') {
                      setSelectedPostMarketStrategy(strategy.id);
                      console.log(`選中盤後選股策略: ${strategy.strategyName} (ID: ${strategy.id})`);
                    }
                  }}
                >
                  <TableCell className="text-white">{index + 1}</TableCell>
                  <TableCell>
                    <span className={cn(
                      "px-2 py-1 rounded text-xs font-medium",
                      strategy.executionStatus === '啟動' ? "bg-green-600 text-white" :
                      strategy.executionStatus === '自動執行' ? "bg-blue-600 text-white" :
                      strategy.executionStatus === '解除自動' ? "bg-orange-600 text-white" :
                      "bg-gray-600 text-gray-300"
                    )}>
                      {strategy.executionStatus}
                    </span>
                  </TableCell>
                  <TableCell className={cn(
                    "font-mono",
                    type === 'postMarket' ? 'text-orange-400' : 'text-yellow-400'
                  )}>
                    {strategy.strategyCode}
                  </TableCell>
                  <TableCell className="text-white">{strategy.strategyName}</TableCell>
                  <TableCell>
                    <span className="px-2 py-1 bg-gray-600 rounded text-xs text-white">
                      {strategy.tradingProduct}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className={cn(
                      "px-2 py-1 rounded text-xs text-white",
                      strategy.direction === '多頭' ? "bg-red-600" :
                      strategy.direction === '空頭' ? "bg-green-600" : "bg-blue-600"
                    )}>
                      {strategy.direction}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="px-2 py-1 bg-blue-600 rounded text-xs text-white">
                      {strategy.frequency}
                    </span>
                  </TableCell>
                  <TableCell className="text-green-400 font-mono">{strategy.activationTime}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 border-gray-600 hover:bg-blue-700 hover:border-blue-600"
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 border-gray-600 hover:bg-red-700 hover:border-red-600"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  };

  if (selectedCategory === 'executionStatus') {
    return (
      <div className="h-full p-2 overflow-auto bg-gray-800">
        <div className="text-center text-white p-8">
          <h2 className="text-2xl font-bold mb-4">執行狀態</h2>
          <p className="text-gray-400">執行狀態監控功能開發中...</p>
        </div>
      </div>
    );
  }

  // 策略設定頁面 - 左右佈局，右側上下分割
  return (
    <div className="h-full bg-gray-900">
      <PanelGroup direction="horizontal" className="h-full" autoSaveId="strategy-horizontal-layout">
        {/* 左側：策略列表 */}
        <Panel defaultSize={30} minSize={15} maxSize={70}>
          <div className="h-full p-2 overflow-auto bg-gray-800">
            <div className="space-y-2">
              <StrategyTable
                title="盤後選股策略"
                strategies={postMarketStrategies}
                type="postMarket"
                color="text-orange-400"
              />
              <StrategyTable
                title="盤中警示策略"
                strategies={alertStrategies}
                type="alert"
                color="text-yellow-400"
              />
              <StrategyTable
                title="盤中交易策略"
                strategies={tradingStrategies}
                type="trading"
                color="text-green-400"
              />
            </div>
          </div>
        </Panel>

        <PanelResizeHandle className="w-3 bg-gray-700 hover:bg-gray-600 cursor-col-resize transition-colors duration-200 flex items-center justify-center group border-l border-r border-gray-600">
          <div className="flex flex-col space-y-1">
            <div className="w-0.5 h-4 bg-gray-500 rounded-full group-hover:bg-gray-400 transition-colors duration-200"></div>
            <div className="w-0.5 h-4 bg-gray-500 rounded-full group-hover:bg-gray-400 transition-colors duration-200"></div>
            <div className="w-0.5 h-4 bg-gray-500 rounded-full group-hover:bg-gray-400 transition-colors duration-200"></div>
          </div>
        </PanelResizeHandle>

        {/* 右側：策略篩選結果和自選股股池 */}
        <Panel defaultSize={70} minSize={30} maxSize={85}>
          <PanelGroup direction="vertical" className="h-full" autoSaveId="strategy-vertical-layout">
            {/* 上半部：策略篩選結果 */}
            <Panel defaultSize={60} minSize={40}>
              <div className="h-full flex flex-col bg-gray-800">
                {/* 策略篩選結果標題和操作按鈕 */}
                <div className="p-3 border-b border-gray-700">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <h2 className="text-lg font-semibold text-white">策略篩選結果</h2>
                      {selectedPostMarketStrategy !== null && (
                        <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">
                          {getStrategyName(selectedPostMarketStrategy)} 執行中
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        className="bg-blue-600 hover:bg-blue-700 text-white h-6 px-2 text-xs"
                        onClick={handleSelectAll}
                      >
                        ✓ 全選
                      </Button>
                      <Button
                        className="bg-green-600 hover:bg-green-700 text-white h-6 px-2 text-xs disabled:opacity-50"
                        onClick={() => handleAddToPool('日內當沖')}
                        disabled={selectedStocks.length === 0}
                      >
                        + 加自選
                      </Button>
                      <Button
                        className="bg-red-600 hover:bg-red-700 text-white h-6 px-2 text-xs"
                        onClick={() => {
                          if (selectedStocks.length === 0) return;

                          console.log(`刪除按鈕被點擊，將刪除 ${selectedStocks.length} 支股票`);

                          // 從stockData中移除選中的股票
                          setStockData(prev => prev.filter(stock => !selectedStocks.includes(stock.id)));

                          // 清空選中狀態
                          setSelectedStocks([]);
                        }}
                        disabled={selectedStocks.length === 0}
                      >
                        ✗ 刪除
                      </Button>
                      <Button
                        className="bg-gray-600 hover:bg-gray-700 text-white h-6 px-2 text-xs"
                        onClick={() => {
                          console.log('取消選擇按鈕被點擊，清空選中狀態');
                          setSelectedStocks([]);
                        }}
                        disabled={selectedStocks.length === 0}
                      >
                        取消選擇
                      </Button>
                      <Button
                        className="bg-gray-600 hover:bg-gray-700 text-white h-6 px-2 text-xs"
                      >
                        匯出
                      </Button>
                      <Button
                        className="bg-purple-600 hover:bg-purple-700 text-white h-6 px-2 text-xs"
                      >
                        執行
                      </Button>
                      <Button
                        className="bg-gray-600 hover:bg-gray-700 text-white h-6 px-2 text-xs"
                      >
                        停止
                      </Button>
                      <Button
                        className="bg-orange-600 hover:bg-orange-700 text-white h-6 px-2 text-xs"
                      >
                        回測
                      </Button>
                      <Button
                        className="bg-yellow-600 hover:bg-yellow-700 text-white h-6 px-2 text-xs"
                        onClick={() => setShowColumnSelector(!showColumnSelector)}
                      >
                        欄位
                      </Button>
                    </div>
                  </div>

                  {/* 日期控制 */}
                  <div className="flex items-center space-x-2">
                    <span className="text-white">最新</span>
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-6 w-6 p-0"
                      onClick={() => changeDate(-1)}
                    >
                      ◀
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-6 w-6 p-0"
                      onClick={() => changeDate(1)}
                    >
                      ▶
                    </Button>
                    <span className="text-white bg-gray-700 px-2 py-1 rounded text-sm">
                      {formatDate(currentDate)} (最新)
                    </span>
                    <span className="text-white">符合筆數(101)</span>
                  </div>
                </div>

                {/* 欄位選擇器 */}
                {showColumnSelector && (
                  <div className="absolute top-16 right-4 bg-gray-700 border border-gray-600 rounded-lg p-4 shadow-lg z-10">
                    <h3 className="text-white font-semibold mb-3">選擇顯示欄位</h3>
                    <div className="space-y-2">
                      {Object.entries(visibleColumns).map(([column, visible]) => (
                        <div key={column} className="flex items-center space-x-2">
                          <Checkbox
                            checked={visible}
                            onCheckedChange={(checked) =>
                              setVisibleColumns(prev => ({ ...prev, [column]: !!checked }))
                            }
                          />
                          <span className="text-white text-sm">{column}</span>
                        </div>
                      ))}
                    </div>
                    <div className="flex justify-end mt-4">
                      <Button
                        size="sm"
                        onClick={() => setShowColumnSelector(false)}
                        className="bg-gray-600 hover:bg-gray-500"
                      >
                        確定
                      </Button>
                    </div>
                  </div>
                )}

                {/* 股票列表 */}
                <div className="flex-1 overflow-auto relative">
              <Table>
                <TableHeader>
                  <TableRow className="border-gray-700">
                    <TableHead className="text-white w-12">選擇</TableHead>
                    {visibleColumns.序號 && <TableHead className="text-white">序號</TableHead>}
                    {visibleColumns.代碼 && <TableHead className="text-white">代碼</TableHead>}
                    {visibleColumns.商品 && <TableHead className="text-white">商品</TableHead>}
                    {visibleColumns.成交 && <TableHead className="text-white">成交</TableHead>}
                    {visibleColumns.漲跌 && <TableHead className="text-white">漲跌%</TableHead>}
                    {visibleColumns.總量 && <TableHead className="text-white">總量</TableHead>}
                    {visibleColumns.月營收月增率 && <TableHead className="text-white">月營收月增率</TableHead>}
                    {visibleColumns.月營收年增率 && <TableHead className="text-white">月營收年增率</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {stockData.map((stock, index) => (
                    <TableRow key={stock.id} className="border-gray-700 hover:bg-transparent">
                      <TableCell>
                        <Checkbox
                          checked={selectedStocks.includes(stock.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedStocks(prev => [...prev, stock.id]);
                            } else {
                              setSelectedStocks(prev => prev.filter(id => id !== stock.id));
                            }
                          }}
                        />
                      </TableCell>
                      {visibleColumns.序號 && <TableCell className="text-white">{index + 1}</TableCell>}
                      {visibleColumns.代碼 && <TableCell className="text-orange-400">{stock.code}</TableCell>}
                      {visibleColumns.商品 && <TableCell className="text-white">{stock.name}</TableCell>}
                      {visibleColumns.成交 && <TableCell className="text-red-400">{stock.price}</TableCell>}
                      {visibleColumns.漲跌 && (
                        <TableCell className={stock.change >= 0 ? "text-red-400" : "text-green-400"}>
                          {stock.change}
                        </TableCell>
                      )}
                      {visibleColumns.總量 && <TableCell className="text-white">{stock.volume}</TableCell>}
                      {visibleColumns.月營收月增率 && <TableCell className="text-red-400">{stock.monthlyGrowth}</TableCell>}
                      {visibleColumns.月營收年增率 && <TableCell className="text-red-400">{stock.yearlyGrowth}</TableCell>}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </Panel>

        <PanelResizeHandle className="h-3 bg-gray-700 hover:bg-gray-600 cursor-row-resize transition-colors duration-200 flex items-center justify-center group border-t border-b border-gray-600">
          <div className="flex space-x-1">
            <div className="h-0.5 w-4 bg-gray-500 rounded-full group-hover:bg-gray-400 transition-colors duration-200"></div>
            <div className="h-0.5 w-4 bg-gray-500 rounded-full group-hover:bg-gray-400 transition-colors duration-200"></div>
            <div className="h-0.5 w-4 bg-gray-500 rounded-full group-hover:bg-gray-400 transition-colors duration-200"></div>
          </div>
        </PanelResizeHandle>

        {/* 下半部：自選股股池 */}
        <Panel defaultSize={40} minSize={30}>
          <div className="h-full flex flex-col bg-gray-800">
            {/* 自選股股池標題和類型選擇 */}
            <div className="p-3 border-b border-gray-700">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-lg font-semibold text-white">自選股股池</h2>
                <div className="flex space-x-1">
                  {['日內當沖', '日波段', '週波段', '月波段', '全部'].map((type) => (
                    <Button
                      key={type}
                      variant={selectedPool === type ? "default" : "outline"}
                      className={cn(
                        "h-6 px-2 text-xs",
                        selectedPool === type
                          ? "bg-red-600 text-white"
                          : "border-gray-600 text-gray-300 hover:bg-gray-700"
                      )}
                      onClick={() => setSelectedPool(type)}
                    >
                      {type}
                    </Button>
                  ))}
                </div>
              </div>

              {/* 操作按鈕 */}
              <div className="flex space-x-2">
                <Button
                  className="bg-green-600 hover:bg-green-700 text-white h-6 px-2 text-xs"
                  onClick={() => {
                    const currentPoolStocks = getCurrentPoolStocks();
                    if (selectedPoolStocks.length === currentPoolStocks.length) {
                      setSelectedPoolStocks([]);
                    } else {
                      setSelectedPoolStocks(currentPoolStocks.map(stock => stock.id));
                    }
                  }}
                >
                  ✓ 全選
                </Button>
                <Button
                  className="bg-blue-600 hover:bg-blue-700 text-white h-6 px-2 text-xs"
                >
                  + 新增股票
                </Button>
                <Button
                  className="bg-red-600 hover:bg-red-700 text-white h-6 px-2 text-xs"
                  onClick={() => {
                    if (selectedPoolStocks.length === 0) return;

                    if (selectedPool === '全部') {
                      // 從所有股池中移除選中的股票
                      setStockPools(prev => ({
                        日內當沖: prev.日內當沖.filter(stock => !selectedPoolStocks.includes(stock.id)),
                        日波段: prev.日波段.filter(stock => !selectedPoolStocks.includes(stock.id)),
                        週波段: prev.週波段.filter(stock => !selectedPoolStocks.includes(stock.id)),
                        月波段: prev.月波段.filter(stock => !selectedPoolStocks.includes(stock.id)),
                        全部: []
                      }));
                    } else {
                      // 從當前股池中移除選中的股票
                      setStockPools(prev => ({
                        ...prev,
                        [selectedPool]: prev[selectedPool as keyof typeof prev].filter(
                          stock => !selectedPoolStocks.includes(stock.id)
                        )
                      }));
                    }

                    // 清空選中狀態
                    setSelectedPoolStocks([]);

                    console.log(`已移除 ${selectedPoolStocks.length} 支股票`);
                  }}
                  disabled={selectedPoolStocks.length === 0}
                >
                  - 移除選中
                </Button>
                <Button
                  className="bg-green-600 hover:bg-green-700 text-white h-6 px-2 text-xs"
                >
                  分析
                </Button>
                <Button
                  className="bg-purple-600 hover:bg-purple-700 text-white h-6 px-2 text-xs"
                >
                  匯出
                </Button>
              </div>
            </div>

            {/* 股池列表 */}
            <div className="flex-1 overflow-auto">
              <Table>
                <TableHeader>
                  <TableRow className="border-gray-700">
                    <TableHead className="text-white w-12">選擇</TableHead>
                    <TableHead className="text-white">代碼</TableHead>
                    <TableHead className="text-white">名稱</TableHead>
                    <TableHead className="text-white">類型</TableHead>
                    <TableHead className="text-white">現價</TableHead>
                    <TableHead className="text-white">漲跌%</TableHead>
                    <TableHead className="text-white">成交量</TableHead>
                    <TableHead className="text-white">加入時間</TableHead>
                    <TableHead className="text-white">備註</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {getCurrentPoolStocks().map((stock) => (
                    <TableRow key={stock.id} className="border-gray-700 hover:bg-transparent">
                      <TableCell>
                        <Checkbox
                          checked={selectedPoolStocks.includes(stock.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedPoolStocks([...selectedPoolStocks, stock.id]);
                            } else {
                              setSelectedPoolStocks(selectedPoolStocks.filter(id => id !== stock.id));
                            }
                          }}
                        />
                      </TableCell>
                      <TableCell className="text-orange-400">{stock.code}</TableCell>
                      <TableCell className="text-white">{stock.name}</TableCell>
                      <TableCell>
                        <span className="bg-red-600 text-white px-2 py-1 rounded text-xs">
                          {stock.type}
                        </span>
                      </TableCell>
                      <TableCell className="text-white">{stock.price}</TableCell>
                      <TableCell className={stock.change >= 0 ? "text-red-400" : "text-green-400"}>
                        {stock.change > 0 ? '+' : ''}{stock.change}%
                      </TableCell>
                      <TableCell className="text-white">{stock.volume.toLocaleString()}</TableCell>
                      <TableCell className="text-gray-400 text-xs">{stock.addTime}</TableCell>
                      <TableCell className="text-gray-400 text-xs">{stock.note}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </Panel>
          </PanelGroup>
        </Panel>
      </PanelGroup>

      {/* 添加到自選股彈窗 */}
      {showAddStockModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-6 w-96">
            <h3 className="text-white font-semibold mb-4">添加到自選股池</h3>
            <p className="text-gray-300 mb-4">
              已選擇 {selectedStocks.length} 支股票，請選擇要添加到的股池類型：
            </p>
            <div className="space-y-2 mb-6">
              {['日內當沖', '日波段', '週波段', '月波段'].map((type) => (
                <Button
                  key={type}
                  variant={stockTypeToAdd === type ? "default" : "outline"}
                  className={cn(
                    "w-full justify-start",
                    stockTypeToAdd === type
                      ? "bg-red-600 text-white"
                      : "border-gray-600 text-gray-300 hover:bg-gray-700"
                  )}
                  onClick={() => setStockTypeToAdd(type)}
                >
                  {type}
                </Button>
              ))}
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowAddStockModal(false);
                  setStockTypeToAdd('');
                }}
                className="border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                取消
              </Button>
              <Button
                onClick={() => {
                  if (selectedStocks.length === 0 || !stockTypeToAdd) return;

                  // 獲取選中的股票數據
                  const selectedStockData = stockData.filter(stock => selectedStocks.includes(stock.id));

                  // 轉換為股池格式，生成新的ID避免衝突
                  const newStocks = selectedStockData.map((stock, index) => ({
                    id: Date.now() + index, // 使用時間戳生成唯一ID
                    code: stock.code,
                    name: stock.name,
                    type: stockTypeToAdd,
                    price: stock.price,
                    change: stock.change,
                    volume: stock.volume,
                    addTime: new Date().toLocaleDateString('zh-TW'),
                    note: '從策略篩選添加'
                  }));

                  // 添加到對應的股池
                  console.log('要添加到的股池類型:', stockTypeToAdd);
                  console.log('要添加的股票:', newStocks);

                  setStockPools(prev => {
                    console.log('添加前的stockPools:', prev);
                    const updatedPools = {
                      ...prev,
                      [stockTypeToAdd]: [...(prev[stockTypeToAdd as keyof typeof prev] || []), ...newStocks]
                    };
                    console.log('添加後的stockPools:', updatedPools);
                    return updatedPools;
                  });

                  console.log(`已添加 ${selectedStocks.length} 支股票到 ${stockTypeToAdd}`);

                  // 關閉模態框並清空狀態
                  setShowAddStockModal(false);
                  setStockTypeToAdd('');
                  setSelectedStocks([]);
                }}
                disabled={!stockTypeToAdd}
                className="bg-green-600 hover:bg-green-700 text-white disabled:opacity-50"
              >
                確定添加
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StrategyContent;
