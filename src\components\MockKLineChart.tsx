import React from 'react';

interface MockKLineChartProps {
  stockCode: string;
  stockName: string;
  timeFrame: string;
  selectedTimeFrames?: string[];
  onTimeFrameSelect?: (timeFrame: string) => void;
  showMarketIndex?: boolean;
  onMarketToggle?: () => void;
}

const MockKLineChart: React.FC<MockKLineChartProps> = ({
  stockCode,
  stockName,
  timeFrame,
  selectedTimeFrames = ['5分', '10分', '30分', '60分', '日', '週', '月', '年'],
  onTimeFrameSelect,
  showMarketIndex = false,
  onMarketToggle
}) => {
  // 模擬數據 - 根據是否顯示大盤來決定
  const mockOpen = showMarketIndex
    ? (Math.random() * 200 + 21800).toFixed(0)  // 大盤指數範圍
    : stockCode ? (Math.random() * 20 + 180).toFixed(0) : '0';  // 個股價格範圍
  const mockHigh = showMarketIndex || stockCode
    ? (parseFloat(mockOpen) + Math.random() * 50).toFixed(0)
    : '0';
  const mockLow = showMarketIndex || stockCode
    ? (parseFloat(mockOpen) - Math.random() * 30).toFixed(0)
    : '0';
  const mockClose = showMarketIndex || stockCode
    ? (parseFloat(mockOpen) + (Math.random() - 0.5) * 40).toFixed(0)
    : '0';
  const mockVolume = showMarketIndex
    ? Math.floor(Math.random() * 50000 + 10000)  // 大盤成交量(億)
    : stockCode ? Math.floor(Math.random() * 50 + 10) : 0;  // 個股成交量(張)
  const mockChange = showMarketIndex || stockCode
    ? (parseFloat(mockClose) - parseFloat(mockOpen)).toFixed(0)
    : '0';

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* 股票信息頭部 */}
      <div className="bg-gray-800 px-3 py-1.5 border-b border-gray-700 flex-shrink-0">
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center space-x-3 text-xs">
            {showMarketIndex ? (
              <>
                <span className="text-white font-medium">台股大盤</span>
                <span className="text-gray-400">2025/06/27</span>
                <span className="text-gray-300">開{mockOpen}</span>
                <span className="text-gray-300">高{mockHigh}</span>
                <span className="text-gray-300">低{mockLow}</span>
                <span className="text-gray-300">收{mockClose}</span>
                <span className="text-gray-300">量{mockVolume}億</span>
                <span className="text-gray-300">漲跌{mockChange}</span>
              </>
            ) : (
              <>
                <span className="text-white font-medium">
                  {stockName}({stockCode})
                </span>
                <span className="text-gray-400">2025/06/27</span>
                {stockCode && (
                  <>
                    <span className="text-gray-300">開{mockOpen}</span>
                    <span className="text-gray-300">高{mockHigh}</span>
                    <span className="text-gray-300">低{mockLow}</span>
                    <span className="text-gray-300">收{mockClose}</span>
                    <span className="text-gray-300">量{mockVolume}</span>
                    <span className="text-gray-300">漲跌{mockChange}</span>
                  </>
                )}
              </>
            )}
          </div>
        </div>

        {/* 時間周期選擇欄位 */}
        <div className="flex items-center space-x-1">
          {selectedTimeFrames.map((period, index) => (
            <button
              key={period}
              onClick={() => onTimeFrameSelect?.(period)}
              className={`px-2 py-0.5 text-xs rounded transition-colors ${
                timeFrame === period && !showMarketIndex
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white'
              }`}
            >
              {period}
            </button>
          ))}

          {/* 大盤按鈕 */}
          <button
            onClick={() => onMarketToggle?.()}
            className={`px-2 py-0.5 text-xs rounded transition-colors ${
              showMarketIndex
                ? 'bg-green-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white'
            }`}
          >
            大盤
          </button>
        </div>
      </div>

      {/* K線圖區域 */}
      <div className="flex-1 relative bg-black p-4">
        {stockCode ? (
          <div className="h-full flex items-center justify-center">
            {/* 模擬K線圖 */}
            <div className="w-full h-full relative">
              {/* 價格軸 */}
              <div className="absolute right-0 top-0 h-full w-12 bg-gray-800 border-l border-gray-600">
                <div className="h-full flex flex-col justify-between py-2 text-xs text-gray-400">
                  <span>{(parseFloat(mockHigh) + 2).toFixed(0)}</span>
                  <span>{(parseFloat(mockHigh) + 1).toFixed(0)}</span>
                  <span>{mockClose}</span>
                  <span>{(parseFloat(mockLow) - 1).toFixed(0)}</span>
                  <span>{(parseFloat(mockLow) - 2).toFixed(0)}</span>
                </div>
              </div>

              {/* 時間軸 */}
              <div className="absolute bottom-0 left-0 right-12 h-6 bg-gray-800 border-t border-gray-600">
                <div className="h-full flex justify-between items-center px-2 text-gray-400" style={{ fontSize: '10px' }}>
                  <span>09:00</span>
                  <span>10:30</span>
                  <span>12:00</span>
                  <span>13:30</span>
                  <span>15:00</span>
                </div>
              </div>

              {/* 模擬K線柱 */}
              <div className="absolute inset-0 right-12 bottom-6">
                <svg className="w-full h-full">
                  {/* 網格線 */}
                  <defs>
                    <pattern id="grid" width="40" height="20" patternUnits="userSpaceOnUse">
                      <path d="M 40 0 L 0 0 0 20" fill="none" stroke="#374151" strokeWidth="0.5"/>
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#grid)" />
                  
                  {/* 模擬K線 */}
                  {Array.from({ length: 20 }, (_, i) => {
                    const x = (i + 1) * 30;
                    const baseY = 150;
                    const isGreen = Math.random() > 0.5;
                    const height = Math.random() * 40 + 10;
                    const wickTop = Math.random() * 10;
                    const wickBottom = Math.random() * 10;
                    
                    return (
                      <g key={i}>
                        {/* 上影線 */}
                        <line 
                          x1={x} y1={baseY - height/2 - wickTop} 
                          x2={x} y2={baseY - height/2}
                          stroke={isGreen ? '#10B981' : '#EF4444'} 
                          strokeWidth="1"
                        />
                        {/* 下影線 */}
                        <line 
                          x1={x} y1={baseY + height/2} 
                          x2={x} y2={baseY + height/2 + wickBottom}
                          stroke={isGreen ? '#10B981' : '#EF4444'} 
                          strokeWidth="1"
                        />
                        {/* K線實體 */}
                        <rect
                          x={x - 6}
                          y={baseY - height/2}
                          width="12"
                          height={height}
                          fill={isGreen ? '#10B981' : '#EF4444'}
                          stroke={isGreen ? '#10B981' : '#EF4444'}
                        />
                      </g>
                    );
                  })}
                  
                  {/* 移動平均線 */}
                  <polyline
                    points="30,160 60,155 90,150 120,145 150,140 180,135 210,130 240,125 270,120 300,115 330,110 360,105 390,100 420,95 450,90 480,85 510,80 540,75 570,70 600,65"
                    fill="none"
                    stroke="#F59E0B"
                    strokeWidth="1"
                  />
                  <polyline
                    points="30,170 60,168 90,165 120,162 150,158 180,155 210,152 240,148 270,145 300,142 330,138 360,135 390,132 420,128 450,125 480,122 510,118 540,115 570,112 600,108"
                    fill="none"
                    stroke="#8B5CF6"
                    strokeWidth="1"
                  />
                </svg>
              </div>

              {/* 技術指標標籤 */}
              <div className="absolute top-2 left-2 flex space-x-4 text-xs">
                <span className="text-yellow-400">MA5: {(parseFloat(mockClose) + 1).toFixed(0)}</span>
                <span className="text-purple-400">MA20: {(parseFloat(mockClose) - 1).toFixed(0)}</span>
                <span className="text-blue-400">MA60: {(parseFloat(mockClose) - 2).toFixed(0)}</span>
              </div>
            </div>
          </div>
        ) : showMarketIndex ? (
          <div className="h-full flex items-center justify-center">
            {/* 大盤模式下顯示大盤K線圖 */}
            <div className="w-full h-full relative">
              {/* 價格軸 */}
              <div className="absolute right-0 top-0 h-full w-12 bg-gray-800 border-l border-gray-600">
                <div className="h-full flex flex-col justify-between py-2 text-xs text-gray-400">
                  <span>{(parseFloat(mockHigh) + 50).toFixed(0)}</span>
                  <span>{(parseFloat(mockHigh) + 25).toFixed(0)}</span>
                  <span>{mockClose}</span>
                  <span>{(parseFloat(mockLow) - 25).toFixed(0)}</span>
                  <span>{(parseFloat(mockLow) - 50).toFixed(0)}</span>
                </div>
              </div>

              {/* 時間軸 */}
              <div className="absolute bottom-0 left-0 right-12 h-6 bg-gray-800 border-t border-gray-600">
                <div className="h-full flex justify-between items-center px-2 text-gray-400" style={{ fontSize: '10px' }}>
                  <span>09:00</span>
                  <span>10:30</span>
                  <span>12:00</span>
                  <span>13:30</span>
                  <span>15:00</span>
                </div>
              </div>

              {/* 大盤K線圖內容 */}
              <div className="absolute inset-0 right-12 bottom-6">
                <svg className="w-full h-full">
                  {/* 網格線 */}
                  <defs>
                    <pattern id="market-grid" width="40" height="20" patternUnits="userSpaceOnUse">
                      <path d="M 40 0 L 0 0 0 20" fill="none" stroke="#374151" strokeWidth="0.5"/>
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#market-grid)" />

                  {/* 大盤K線 */}
                  {Array.from({ length: 20 }, (_, i) => {
                    const x = (i + 1) * 30;
                    const baseY = 120;
                    const isGreen = Math.random() > 0.4; // 大盤較多上漲
                    const height = Math.random() * 30 + 15;
                    const wickTop = Math.random() * 8;
                    const wickBottom = Math.random() * 8;

                    return (
                      <g key={i}>
                        {/* 上影線 */}
                        <line
                          x1={x} y1={baseY - height/2 - wickTop}
                          x2={x} y2={baseY - height/2}
                          stroke={isGreen ? '#10B981' : '#EF4444'}
                          strokeWidth="1"
                        />
                        {/* 下影線 */}
                        <line
                          x1={x} y1={baseY + height/2}
                          x2={x} y2={baseY + height/2 + wickBottom}
                          stroke={isGreen ? '#10B981' : '#EF4444'}
                          strokeWidth="1"
                        />
                        {/* K線實體 */}
                        <rect
                          x={x - 6}
                          y={baseY - height/2}
                          width="12"
                          height={height}
                          fill={isGreen ? '#10B981' : '#EF4444'}
                          stroke={isGreen ? '#10B981' : '#EF4444'}
                        />
                      </g>
                    );
                  })}

                  {/* 大盤移動平均線 */}
                  <polyline
                    points="30,130 60,128 90,125 120,122 150,118 180,115 210,112 240,108 270,105 300,102 330,98 360,95 390,92 420,88 450,85 480,82 510,78 540,75 570,72 600,68"
                    fill="none"
                    stroke="#F59E0B"
                    strokeWidth="1"
                  />
                  <polyline
                    points="30,140 60,138 90,136 120,133 150,130 180,127 210,124 240,120 270,117 300,114 330,110 360,107 390,104 420,100 450,97 480,94 510,90 540,87 570,84 600,80"
                    fill="none"
                    stroke="#8B5CF6"
                    strokeWidth="1"
                  />
                </svg>
              </div>

              {/* 大盤技術指標標籤 */}
              <div className="absolute top-2 left-2 flex space-x-4 text-xs">
                <span className="text-yellow-400">MA5: {(parseFloat(mockClose) + 25).toFixed(0)}</span>
                <span className="text-purple-400">MA20: {(parseFloat(mockClose) - 15).toFixed(0)}</span>
                <span className="text-blue-400">MA60: {(parseFloat(mockClose) - 35).toFixed(0)}</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center text-gray-500">
            請從左側選擇個股
          </div>
        )}
      </div>

      {/* 成交量區域 */}
      {(stockCode || showMarketIndex) && (
        <div className="h-20 bg-gray-900 border-t border-gray-700 relative">
          <div className="absolute right-0 top-0 h-full w-12 bg-gray-800 border-l border-gray-600">
            <div className="h-full flex flex-col justify-between py-1 text-xs text-gray-400">
              <span>50K</span>
              <span>25K</span>
              <span>0</span>
            </div>
          </div>
          <div className="h-full right-12 relative">
            <svg className="w-full h-full">
              {/* 成交量柱狀圖 */}
              {Array.from({ length: 20 }, (_, i) => {
                const x = (i + 1) * 30;
                const height = Math.random() * 60 + 5;
                const isGreen = Math.random() > 0.5;
                
                return (
                  <rect
                    key={i}
                    x={x - 4}
                    y={80 - height}
                    width="8"
                    height={height}
                    fill={isGreen ? '#10B981' : '#EF4444'}
                    opacity="0.7"
                  />
                );
              })}
            </svg>
          </div>
          <div className="absolute bottom-1 left-2 text-xs text-gray-400">
            成交量: {showMarketIndex
              ? `${mockVolume}億`
              : stockCode
                ? `${(Math.random() * 10000 + 1000).toFixed(0)}張`
                : '0張'
            }
          </div>
        </div>
      )}
    </div>
  );
};

export default MockKLineChart;
