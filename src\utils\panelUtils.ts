import { PanelConfig } from '../types/PanelTypes';

// 面板ID生成器
export const generatePanelId = (market: 'taiwan' | 'us', component: string): string => {
  const prefix = market === 'taiwan' ? 'tw' : 'us';
  const componentName = component.toLowerCase().replace(/([A-Z])/g, '-$1').replace(/^-/, '');
  return `${prefix}-${componentName}`;
};

// 根據DOM元素查找面板ID
export const findPanelIdFromElement = (element: HTMLElement): string | null => {
  // 查找最近的帶有 data-panel-id 的父元素
  let current = element;
  while (current && current !== document.body) {
    const panelId = current.getAttribute('data-panel-id');
    if (panelId) return panelId;
    current = current.parentElement!;
  }
  return null;
};

// 根據位置獲取面板元素
export const getPanelElement = (panelId: string): HTMLElement | null => {
  return document.querySelector(`[data-panel-id="${panelId}"]`);
};

// 獲取所有面板元素
export const getAllPanelElements = (): NodeListOf<HTMLElement> => {
  return document.querySelectorAll('[data-panel-id]');
};

// 面板配置驗證
export const validatePanelConfig = (config: Partial<PanelConfig>): boolean => {
  if (!config.id || typeof config.id !== 'string') return false;
  if (config.size && (config.size.width < 0 || config.size.height < 0)) return false;
  if (config.position && !['topLeft', 'topRight', 'bottomLeft', 'bottomRight'].includes(config.position)) return false;
  return true;
};

// 面板位置轉換工具
export const positionToCoordinates = (position: string) => {
  switch (position) {
    case 'topLeft': return { x: 0, y: 0 };
    case 'topRight': return { x: 1, y: 0 };
    case 'bottomLeft': return { x: 0, y: 1 };
    case 'bottomRight': return { x: 1, y: 1 };
    default: return { x: 0, y: 0 };
  }
};

// 座標轉換為位置
export const coordinatesToPosition = (x: number, y: number): string => {
  if (x === 0 && y === 0) return 'topLeft';
  if (x === 1 && y === 0) return 'topRight';
  if (x === 0 && y === 1) return 'bottomLeft';
  if (x === 1 && y === 1) return 'bottomRight';
  return 'topLeft';
};

// 面板狀態序列化
export const serializePanelState = (panels: Record<string, PanelConfig>): string => {
  return JSON.stringify(panels, null, 2);
};

// 面板狀態反序列化
export const deserializePanelState = (serialized: string): Record<string, PanelConfig> | null => {
  try {
    const parsed = JSON.parse(serialized);
    // 驗證每個面板配置
    for (const [id, config] of Object.entries(parsed)) {
      if (!validatePanelConfig(config as PanelConfig)) {
        console.warn(`Invalid panel config for ${id}`);
        return null;
      }
    }
    return parsed;
  } catch (error) {
    console.error('Failed to deserialize panel state:', error);
    return null;
  }
};