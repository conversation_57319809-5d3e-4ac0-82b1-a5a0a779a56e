import React from 'react';
import { 
  Calendar, 
  MessageSquare, 
  Paperclip,
  Flag,
  Flame,
  AlertTriangle,
  Zap,
  MoreHorizontal,
  Clock,
  CheckCircle2
} from 'lucide-react';

interface Task {
  id: string;
  title: string;
  description: string;
  status: 'todo' | 'in-progress' | 'review' | 'done';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignee: {
    name: string;
    avatar: string;
    initials: string;
  };
  dueDate: string;
  project: string;
  tags: string[];
  comments: number;
  attachments: number;
  progress?: number;
}

interface TaskCardProps {
  task: Task;
}

const TaskCard: React.FC<TaskCardProps> = ({ task }) => {
  const getPriorityConfig = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return { 
          icon: Zap, 
          color: 'text-red-600 dark:text-red-400', 
          bg: 'bg-red-100 dark:bg-red-900/30',
          label: 'Urgent'
        };
      case 'high':
        return { 
          icon: Flag, 
          color: 'text-orange-600 dark:text-orange-400', 
          bg: 'bg-orange-100 dark:bg-orange-900/30',
          label: 'High'
        };
      case 'medium':
        return { 
          icon: Flame, 
          color: 'text-yellow-600 dark:text-yellow-400', 
          bg: 'bg-yellow-100 dark:bg-yellow-900/30',
          label: 'Medium'
        };
      default:
        return { 
          icon: Clock, 
          color: 'text-gray-600 dark:text-gray-400', 
          bg: 'bg-gray-100 dark:bg-gray-700',
          label: 'Low'
        };
    }
  };

  const getTagColor = (index: number) => {
    const colors = [
      'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
      'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300',
      'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300',
    ];
    return colors[index % colors.length];
  };

  const priorityConfig = getPriorityConfig(task.priority);
  const PriorityIcon = priorityConfig.icon;

  return (
    <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-5 shadow-sm border border-gray-200/50 dark:border-gray-700/50 hover:shadow-lg hover:shadow-gray-200/50 dark:hover:shadow-gray-900/20 transition-all duration-300 hover:transform hover:scale-[1.02] group cursor-pointer">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className={`flex items-center space-x-2 px-2 py-1 rounded-lg ${priorityConfig.bg}`}>
          <PriorityIcon className={`h-3 w-3 ${priorityConfig.color}`} />
          <span className={`text-xs font-medium ${priorityConfig.color}`}>
            {priorityConfig.label}
          </span>
        </div>
        <button className="opacity-0 group-hover:opacity-100 p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200">
          <MoreHorizontal className="h-4 w-4" />
        </button>
      </div>

      {/* Title and Description */}
      <div className="mb-4">
        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2 leading-tight">
          {task.title}
        </h4>
        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 leading-relaxed">
          {task.description}
        </p>
      </div>

      {/* Progress Bar (for in-progress tasks) */}
      {task.progress !== undefined && (
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Progress</span>
            <span className="text-xs font-semibold text-gray-900 dark:text-white">{task.progress}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${task.progress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Tags */}
      <div className="flex flex-wrap gap-1.5 mb-4">
        {task.tags.slice(0, 2).map((tag, index) => (
          <span
            key={index}
            className={`px-2 py-1 text-xs font-medium rounded-md ${getTagColor(index)}`}
          >
            {tag}
          </span>
        ))}
        {task.tags.length > 2 && (
          <span className="px-2 py-1 text-xs font-medium rounded-md bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400">
            +{task.tags.length - 2}
          </span>
        )}
      </div>

      {/* Project */}
      <div className="mb-4">
        <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
          {task.project}
        </span>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between pt-3 border-t border-gray-100 dark:border-gray-700">
        {/* Assignee */}
        <div className="flex items-center space-x-2">
          <div className="w-7 h-7 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-sm">
            <span className="text-white text-xs font-semibold">{task.assignee.initials}</span>
          </div>
          <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
            {task.assignee.name.split(' ')[0]}
          </span>
        </div>

        {/* Meta Info */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-1 text-gray-500 dark:text-gray-400">
            <Calendar className="h-3 w-3" />
            <span className="text-xs font-medium">{task.dueDate}</span>
          </div>
          
          {task.comments > 0 && (
            <div className="flex items-center space-x-1 text-gray-500 dark:text-gray-400">
              <MessageSquare className="h-3 w-3" />
              <span className="text-xs font-medium">{task.comments}</span>
            </div>
          )}
          
          {task.attachments > 0 && (
            <div className="flex items-center space-x-1 text-gray-500 dark:text-gray-400">
              <Paperclip className="h-3 w-3" />
              <span className="text-xs font-medium">{task.attachments}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TaskCard;