import React, { useState, useCallback } from 'react';
import { Plus, X, Settings, ChevronDown } from 'lucide-react';
import MockKLine<PERSON>hart from './MockKLineChart';

interface KLineSubWindow {
  id: string;
  title: string;
  timeFrame: string; // 當前顯示的時間周期
  selectedTimeFrames: string[]; // 使用者選擇的時間周期列表
  stockCode: string;
  stockName: string;
  height: number; // 百分比
  showSettings: boolean;
  showMarketIndex: boolean; // 是否顯示大盤
}

interface RightPanelContainerProps {
  className?: string;
  style?: React.CSSProperties;
  selectedStock?: {
    code: string;
    name: string;
  } | null;
}

const RightPanelContainer: React.FC<RightPanelContainerProps> = ({ className, style, selectedStock }) => {
  // 新增子視窗的狀態提示
  const [showAddSuccess, setShowAddSuccess] = useState(false);
  // K線圖子視窗配置 - 預設1個子視窗
  const [klineSubWindows, setKlineSubWindows] = useState<KLineSubWindow[]>([
    {
      id: 'kline-1',
      title: 'K線圖 1',
      timeFrame: '日',
      selectedTimeFrames: ['5分', '10分', '30分', '60分', '日', '週', '月', '年'],
      stockCode: selectedStock?.code || '',
      stockName: selectedStock?.name || '請選擇個股',
      height: 100, // 單一視窗佔滿100%
      showSettings: false,
      showMarketIndex: false
    }
  ]);

  // 可用的時間周期選項
  const allTimeFrameOptions = ['5分', '10分', '30分', '60分', '日', '週', '月', '年', '季', '半年'];

  // 計算高度分配 - 確保總和為100%
  const calculateHeights = useCallback((windowCount: number) => {
    if (windowCount === 0) return [];

    const baseHeight = Math.floor(100 / windowCount);
    const remainder = 100 - (baseHeight * windowCount);

    // 將餘數分配給前幾個視窗
    const heights = Array(windowCount).fill(baseHeight);
    for (let i = 0; i < remainder; i++) {
      heights[i] += 1;
    }

    // 驗證總和為100%
    const total = heights.reduce((sum, h) => sum + h, 0);
    console.log(`高度分配: ${windowCount}個視窗 -> ${heights.join('% + ')}% = ${total}%`);

    return heights;
  }, []);

  // 當選中的個股變更時，更新所有子視窗的個股信息
  React.useEffect(() => {
    if (selectedStock) {
      setKlineSubWindows(prev =>
        prev.map(window => ({
          ...window,
          stockCode: selectedStock.code,
          stockName: selectedStock.name
        }))
      );
    }
  }, [selectedStock]);

  // 當只有一個子視窗時，自動添加大盤子視窗
  React.useEffect(() => {
    if (klineSubWindows.length === 1 && !klineSubWindows[0].showMarketIndex) {
      // 檢查是否已經有大盤視窗
      const hasMarketWindow = klineSubWindows.some(window => window.showMarketIndex);

      if (!hasMarketWindow) {
        const marketWindow: KLineSubWindow = {
          id: 'market-window',
          title: '大盤視窗',
          timeFrame: '日',
          selectedTimeFrames: ['5分', '10分', '30分', '60分', '日', '週', '月', '年'],
          stockCode: '',
          stockName: '台股大盤',
          height: 50,
          showSettings: false,
          showMarketIndex: true // 預設顯示大盤
        };

        // 調整高度：個股視窗50%，大盤視窗50%
        setKlineSubWindows(prev => [
          { ...prev[0], height: 50 },
          marketWindow
        ]);
      }
    }
  }, [klineSubWindows.length]);

  // 添加新的子視窗
  const addSubWindow = useCallback(() => {
    const userWindowCount = klineSubWindows.filter(w => w.id !== 'market-window').length;
    if (userWindowCount >= 3) return; // 最多3個用戶子視窗

    setKlineSubWindows(prev => {
      // 檢查是否有自動添加的大盤視窗
      const marketWindowIndex = prev.findIndex(window => window.id === 'market-window');

      if (marketWindowIndex !== -1) {
        // 如果有大盤視窗，將其轉換為第2個用戶子視窗
        const newWindows = [...prev];
        newWindows[marketWindowIndex] = {
          ...newWindows[marketWindowIndex],
          id: `kline-2`,
          title: 'K線圖 2',
          stockCode: selectedStock?.code || '',
          stockName: selectedStock?.name || '請選擇個股',
          showMarketIndex: false // 轉換為個股視窗
        };

        // 重新分配高度 - 2個視窗各50%
        const heights = calculateHeights(2);
        return newWindows.map((window, index) => ({
          ...window,
          height: heights[index]
        }));
      } else {
        // 正常添加新視窗的邏輯
        const newId = `kline-${userWindowCount + 1}`;
        const newWindow: KLineSubWindow = {
          id: newId,
          title: `K線圖 ${userWindowCount + 1}`,
          timeFrame: '日',
          selectedTimeFrames: ['5分', '10分', '30分', '60分', '日', '週', '月', '年'],
          stockCode: selectedStock?.code || '',
          stockName: selectedStock?.name || '請選擇個股',
          height: 0, // 臨時值，下面會重新計算
          showSettings: false,
          showMarketIndex: false
        };

        // 重新分配高度 - 確保總和為100%
        const newWindowCount = prev.length + 1;
        const heights = calculateHeights(newWindowCount);

        return [
          ...prev.map((window, index) => ({ ...window, height: heights[index] })),
          { ...newWindow, height: heights[newWindowCount - 1] }
        ];
      }
    });

    // 顯示成功提示
    setShowAddSuccess(true);
    setTimeout(() => setShowAddSuccess(false), 2000);
  }, [klineSubWindows, selectedStock, calculateHeights]);

  // 移除子視窗
  const removeSubWindow = useCallback((windowId: string) => {
    const userWindowCount = klineSubWindows.filter(w => w.id !== 'market-window').length;
    if (userWindowCount <= 1 && windowId !== 'market-window') return; // 至少保留1個用戶子視窗

    setKlineSubWindows(prev => {
      const filtered = prev.filter(window => window.id !== windowId);
      const remainingUserWindows = filtered.filter(w => w.id !== 'market-window');

      // 如果移除後只剩一個用戶視窗，將其設為R1並添加大盤視窗
      if (remainingUserWindows.length === 1) {
        const userWindow = {
          ...remainingUserWindows[0],
          height: 50,
          title: 'K線圖 1'
        };

        // 自動添加大盤視窗（會被useEffect處理）
        return [userWindow];
      }

      // 正常情況下重新分配編號和高度
      const heights = calculateHeights(filtered.length);
      let userIndex = 1;

      return filtered.map((window, index) => {
        if (window.showMarketIndex) {
          return {
            ...window,
            height: heights[index],
            title: '大盤視窗'
          };
        } else {
          const result = {
            ...window,
            height: heights[index],
            title: `K線圖 ${userIndex}`
          };
          userIndex++;
          return result;
        }
      });
    });
  }, [klineSubWindows, calculateHeights]);

  // 更新子視窗當前顯示的時間周期
  const updateTimeFrame = useCallback((windowId: string, timeFrame: string) => {
    setKlineSubWindows(prev =>
      prev.map(window =>
        window.id === windowId ? { ...window, timeFrame } : window
      )
    );
  }, []);

  // 更新子視窗的時間周期選擇列表
  const updateSelectedTimeFrames = useCallback((windowId: string, selectedTimeFrames: string[]) => {
    setKlineSubWindows(prev =>
      prev.map(window =>
        window.id === windowId
          ? {
              ...window,
              selectedTimeFrames,
              // 如果當前時間周期不在新選擇的列表中，則設為第一個
              timeFrame: selectedTimeFrames.includes(window.timeFrame)
                ? window.timeFrame
                : selectedTimeFrames[0] || '日'
            }
          : window
      )
    );
  }, []);

  // 切換時間周期選項的選中狀態
  const toggleTimeFrameOption = useCallback((windowId: string, option: string) => {
    setKlineSubWindows(prev =>
      prev.map(window => {
        if (window.id !== windowId) return window;

        const currentSelected = window.selectedTimeFrames;
        const isSelected = currentSelected.includes(option);

        let newSelected: string[];
        if (isSelected) {
          // 取消選擇，但至少保留一個
          newSelected = currentSelected.length > 1
            ? currentSelected.filter(item => item !== option)
            : currentSelected;
        } else {
          // 添加選擇，但最多8個
          newSelected = currentSelected.length < 8
            ? [...currentSelected, option]
            : currentSelected;
        }

        return {
          ...window,
          selectedTimeFrames: newSelected,
          // 如果當前顯示的時間周期被取消選擇，則切換到第一個
          timeFrame: newSelected.includes(window.timeFrame)
            ? window.timeFrame
            : newSelected[0] || '日'
        };
      })
    );
  }, []);

  // 切換設定面板顯示
  const toggleSettings = useCallback((windowId: string) => {
    setKlineSubWindows(prev =>
      prev.map(window =>
        window.id === windowId
          ? { ...window, showSettings: !window.showSettings }
          : { ...window, showSettings: false } // 關閉其他設定面板
      )
    );
  }, []);

  // 切換大盤顯示
  const toggleMarketIndex = useCallback((windowId: string) => {
    setKlineSubWindows(prev =>
      prev.map(window =>
        window.id === windowId
          ? { ...window, showMarketIndex: !window.showMarketIndex }
          : window
      )
    );
  }, []);

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* 頂部控制欄 */}
      <div className="bg-gray-800 px-3 py-2 border-b border-gray-700 flex items-center justify-between flex-shrink-0">
        <div className="flex items-center space-x-2">
          <span className="text-white text-sm font-medium">K線圖視窗</span>
          <span className="text-gray-400 text-xs">
            ({klineSubWindows.filter(w => w.id !== 'market-window').length}/3)
            {klineSubWindows.some(w => w.id === 'market-window') && (
              <span className="text-green-400 ml-1">+大盤</span>
            )}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          {/* 設定按鈕和子視窗選擇 */}
          {klineSubWindows.length > 0 && (
            <div className="flex items-center space-x-1">
              {klineSubWindows.length > 1 && (
                <select
                  onChange={(e) => {
                    const windowId = e.target.value;
                    if (windowId) {
                      toggleSettings(windowId);
                    }
                  }}
                  className="bg-gray-700 text-white text-xs px-2 py-1 rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
                  defaultValue=""
                >
                  <option value="" disabled>選擇視窗</option>
                  {klineSubWindows.map((window, index) => (
                    <option key={window.id} value={window.id}>
                      視窗 {index + 1} ({window.timeFrame})
                    </option>
                  ))}
                </select>
              )}
              <button
                onClick={() => toggleSettings(klineSubWindows[0].id)}
                className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
                title="設定"
              >
                <Settings className="w-4 h-4" />
              </button>
            </div>
          )}

          {/* 新增子視窗按鈕 */}
          {(() => {
            const userWindowCount = klineSubWindows.filter(w => w.id !== 'market-window').length;
            const hasMarketWindow = klineSubWindows.some(w => w.id === 'market-window');

            return userWindowCount < 3 ? (
              <button
                onClick={addSubWindow}
                className="flex items-center space-x-1 px-2 py-1 text-gray-300 hover:text-white hover:bg-gray-700 rounded transition-colors border border-gray-600 hover:border-gray-500"
                title={hasMarketWindow ? `轉換大盤視窗` : `新增子視窗`}
              >
                <Plus className="w-3 h-3" />
                <span className="text-xs">
                  {hasMarketWindow ? '新增視窗' : `新增視窗`}
                </span>
              </button>
            ) : (
              <div className="flex items-center space-x-1 px-2 py-1 text-gray-500 bg-gray-800 rounded border border-gray-700">
                <span className="text-xs">已達上限 (3/3)</span>
              </div>
            );
          })()}
        </div>
      </div>

      {/* 分割線 - 不可移動，標準樣式 */}
      <div className="h-px bg-gray-400"></div>

      {/* 新增成功提示 */}
      {showAddSuccess && (
        <div className="bg-green-600 text-white px-3 py-2 text-xs flex items-center justify-center border-b border-green-500">
          <span>✓ 成功新增子視窗</span>
        </div>
      )}

      {/* 子視窗容器 */}
      <div className="flex-1 flex flex-col">
        {klineSubWindows.map((window, index) => (
          <React.Fragment key={window.id}>
            {/* 子視窗內容 */}
            <div
              className="relative"
              style={{ height: `${window.height}%` }}
            >
            {/* 視窗控制按鈕 - 右上角 */}
            <div className="absolute top-1 right-1 flex items-center space-x-1 z-10">
              {/* 移除按鈕 - 只在多個子視窗時顯示 */}
              {klineSubWindows.length > 1 && (
                <button
                  onClick={() => removeSubWindow(window.id)}
                  className="p-1 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded transition-colors"
                  title="移除視窗"
                >
                  <X className="w-3 h-3" />
                </button>
              )}
            </div>

            {/* 設定面板 */}
            {window.showSettings && (
              <div className="bg-gray-700 px-3 py-3 border-b border-gray-600 flex-shrink-0">
                <div className="space-y-3">
                  {/* 時間周期選擇 */}
                  <div>
                    <div className="text-gray-300 text-xs mb-2">選擇時間周期 (最多8個):</div>
                    <div className="grid grid-cols-5 gap-1">
                      {allTimeFrameOptions.map(option => (
                        <button
                          key={option}
                          onClick={() => toggleTimeFrameOption(window.id, option)}
                          className={`px-2 py-1 text-xs rounded transition-colors ${
                            window.selectedTimeFrames.includes(option)
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                          }`}
                        >
                          {option}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* 已選擇的時間周期顯示 */}
                  <div>
                    <div className="text-gray-300 text-xs mb-1">
                      已選擇 ({window.selectedTimeFrames.length}/8):
                    </div>
                    <div className="text-gray-400 text-xs">
                      {window.selectedTimeFrames.join(', ')}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* K線圖內容 */}
            <div className="flex-1 overflow-hidden">
              <MockKLineChart
                stockCode={window.stockCode}
                stockName={window.stockName}
                timeFrame={window.timeFrame}
                selectedTimeFrames={window.selectedTimeFrames}
                onTimeFrameSelect={(timeFrame) => updateTimeFrame(window.id, timeFrame)}
                showMarketIndex={window.showMarketIndex}
                onMarketToggle={() => toggleMarketIndex(window.id)}
              />
            </div>
            </div>

            {/* 分割線 - 除了最後一個子視窗，標準樣式 */}
            {index < klineSubWindows.length - 1 && (
              <div
                className="relative h-2 cursor-row-resize group flex-shrink-0 bg-transparent hover:bg-gray-800/20"
                onMouseDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();

                  const startY = e.clientY;
                  const currentWindow = klineSubWindows[index];
                  const nextWindow = klineSubWindows[index + 1];
                  const startCurrentHeight = currentWindow.height;
                  const startNextHeight = nextWindow.height;

                  console.log('開始拖拽:', {
                    index,
                    currentHeight: startCurrentHeight,
                    nextHeight: startNextHeight,
                    startY
                  });

                  const handleMouseMove = (e: MouseEvent) => {
                    const deltaY = e.clientY - startY;
                    // 使用固定的容器高度參考值
                    const containerHeight = 400; // 或者動態獲取
                    const deltaPercent = (deltaY / containerHeight) * 100;

                    const newCurrentHeight = Math.max(10, Math.min(90, startCurrentHeight + deltaPercent));
                    const newNextHeight = Math.max(10, Math.min(90, startNextHeight - deltaPercent));

                    // 確保總和接近100%
                    const totalOtherHeight = klineSubWindows
                      .filter((_, i) => i !== index && i !== index + 1)
                      .reduce((sum, w) => sum + w.height, 0);

                    const availableHeight = 100 - totalOtherHeight;
                    const ratio = availableHeight / (newCurrentHeight + newNextHeight);

                    const finalCurrentHeight = newCurrentHeight * ratio;
                    const finalNextHeight = newNextHeight * ratio;

                    console.log('拖拽中:', {
                      deltaY,
                      deltaPercent,
                      finalCurrentHeight,
                      finalNextHeight
                    });

                    setKlineSubWindows(prev =>
                      prev.map((w, i) => {
                        if (i === index) return { ...w, height: finalCurrentHeight };
                        if (i === index + 1) return { ...w, height: finalNextHeight };
                        return w;
                      })
                    );
                  };

                  const handleMouseUp = () => {
                    console.log('結束拖拽');
                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mouseup', handleMouseUp);
                    document.body.style.cursor = '';
                    document.body.style.userSelect = '';
                  };

                  document.addEventListener('mousemove', handleMouseMove);
                  document.addEventListener('mouseup', handleMouseUp);
                  document.body.style.cursor = 'row-resize';
                  document.body.style.userSelect = 'none';
                }}
              >
                {/* 視覺上的細線 */}
                <div className="absolute inset-x-0 top-1/2 transform -translate-y-1/2 h-px bg-gray-400 group-hover:bg-cyan-400 transition-colors pointer-events-none"></div>
                {/* 調試用：顯示可拖拽區域 */}
                <div className="absolute inset-0 group-hover:bg-cyan-400/10 transition-colors"></div>
              </div>
            )}
          </React.Fragment>
        ))}
      </div>

      {/* 無個股選擇時的提示 */}
      {!selectedStock && (
        <div className="absolute inset-0 bg-gray-900/80 flex items-center justify-center">
          <div className="text-center text-gray-400">
            <div className="text-lg mb-2">請選擇個股</div>
            <div className="text-sm mb-4">從左側面板選擇一個個股以顯示K線圖</div>
            {klineSubWindows.length < 3 && (
              <div className="text-xs text-gray-500">
                提示：可新增最多3個子視窗
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default RightPanelContainer;