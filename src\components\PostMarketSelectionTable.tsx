import React, { useState } from 'react';
import { Check, X, Plus, Edit, Trash2, Play, Pause } from 'lucide-react';

interface PostMarketStrategy {
  id: string;
  enabled: boolean;
  strategyCode: string;
  strategyName: string;
  tradingProduct: string;
  direction: '多' | '空' | '多空';
  frequency: string;
  activationTime: string | null;
}

const PostMarketSelectionTable: React.FC = () => {
  const [strategies, setStrategies] = useState<PostMarketStrategy[]>([
    {
      id: '1',
      enabled: true,
      strategyCode: 'PMS001',
      strategyName: '均線突破選股',
      tradingProduct: '台股上市',
      direction: '多',
      frequency: '每日',
      activationTime: '2025-06-29 15:30:25'
    },
    {
      id: '2',
      enabled: false,
      strategyCode: 'PMS002',
      strategyName: 'RSI超賣選股',
      tradingProduct: '台股上櫃',
      direction: '多',
      frequency: '每日',
      activationTime: null
    },
    {
      id: '3',
      enabled: true,
      strategyCode: 'PMS003',
      strategyName: '布林通道突破',
      tradingProduct: '台股ETF',
      direction: '多空',
      frequency: '每週',
      activationTime: '2025-06-29 14:45:12'
    },
    {
      id: '4',
      enabled: false,
      strategyCode: 'PMS004',
      strategyName: '動量反轉選股',
      tradingProduct: '台股上市',
      direction: '空',
      frequency: '每日',
      activationTime: null
    },
    {
      id: '5',
      enabled: true,
      strategyCode: 'PMS005',
      strategyName: '成交量異常選股',
      tradingProduct: '台股上櫃',
      direction: '多',
      frequency: '即時',
      activationTime: '2025-06-29 16:15:08'
    }
  ]);

  const getCurrentDateTime = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  const toggleStrategy = (id: string) => {
    setStrategies(prev => 
      prev.map(strategy => {
        if (strategy.id === id) {
          const newEnabled = !strategy.enabled;
          return {
            ...strategy,
            enabled: newEnabled,
            activationTime: newEnabled ? getCurrentDateTime() : null
          };
        }
        return strategy;
      })
    );
  };

  const addNewStrategy = () => {
    const newStrategy: PostMarketStrategy = {
      id: String(strategies.length + 1),
      enabled: false,
      strategyCode: `PMS${String(strategies.length + 1).padStart(3, '0')}`,
      strategyName: '新策略',
      tradingProduct: '台股上市',
      direction: '多',
      frequency: '每日',
      activationTime: null
    };
    setStrategies(prev => [...prev, newStrategy]);
  };

  const deleteStrategy = (id: string) => {
    setStrategies(prev => prev.filter(strategy => strategy.id !== id));
  };

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      {/* 表格標題 */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-white">盤後選股策略</h2>
        <button
          onClick={addNewStrategy}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-white"
        >
          <Plus className="w-4 h-4" />
          <span>新增策略</span>
        </button>
      </div>

      {/* 表格 */}
      <div className="overflow-x-auto">
        <table className="w-full text-sm text-white">
          <thead>
            <tr className="border-b border-gray-600">
              <th className="text-left py-3 px-4 font-medium text-gray-300">啟動</th>
              <th className="text-left py-3 px-4 font-medium text-gray-300">策略代號</th>
              <th className="text-left py-3 px-4 font-medium text-gray-300">策略名稱</th>
              <th className="text-left py-3 px-4 font-medium text-gray-300">交易商品</th>
              <th className="text-left py-3 px-4 font-medium text-gray-300">多空方向</th>
              <th className="text-left py-3 px-4 font-medium text-gray-300">指定頻率</th>
              <th className="text-left py-3 px-4 font-medium text-gray-300">啟動時間</th>
              <th className="text-left py-3 px-4 font-medium text-gray-300">操作</th>
            </tr>
          </thead>
          <tbody>
            {strategies.map((strategy) => (
              <tr key={strategy.id} className="border-b border-gray-700 hover:bg-gray-700/50">
                {/* 啟動狀態 */}
                <td className="py-3 px-4">
                  <button
                    onClick={() => toggleStrategy(strategy.id)}
                    className={`w-6 h-6 rounded border-2 flex items-center justify-center transition-colors ${
                      strategy.enabled
                        ? 'bg-green-600 border-green-600 text-white'
                        : 'border-gray-500 hover:border-gray-400'
                    }`}
                  >
                    {strategy.enabled && <Check className="w-4 h-4" />}
                  </button>
                </td>

                {/* 策略代號 */}
                <td className="py-3 px-4">
                  <span className="font-mono text-blue-400">{strategy.strategyCode}</span>
                </td>

                {/* 策略名稱 */}
                <td className="py-3 px-4">
                  <span className="font-medium">{strategy.strategyName}</span>
                </td>

                {/* 交易商品 */}
                <td className="py-3 px-4">
                  <span className="px-2 py-1 bg-gray-600 rounded text-xs">{strategy.tradingProduct}</span>
                </td>

                {/* 多空方向 */}
                <td className="py-3 px-4">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    strategy.direction === '多' ? 'bg-red-600 text-white' :
                    strategy.direction === '空' ? 'bg-green-600 text-white' :
                    'bg-purple-600 text-white'
                  }`}>
                    {strategy.direction}
                  </span>
                </td>

                {/* 指定頻率 */}
                <td className="py-3 px-4">
                  <span className={`px-2 py-1 rounded text-xs ${
                    strategy.frequency === '即時' ? 'bg-orange-600 text-white' :
                    strategy.frequency === '每日' ? 'bg-blue-600 text-white' :
                    'bg-indigo-600 text-white'
                  }`}>
                    {strategy.frequency}
                  </span>
                </td>

                {/* 啟動時間 */}
                <td className="py-3 px-4">
                  {strategy.activationTime ? (
                    <span className="font-mono text-green-400 text-xs">
                      {strategy.activationTime}
                    </span>
                  ) : (
                    <span className="text-gray-500 text-xs">未啟動</span>
                  )}
                </td>

                {/* 操作按鈕 */}
                <td className="py-3 px-4">
                  <div className="flex items-center space-x-2">
                    <button
                      className="p-1 text-gray-400 hover:text-blue-400 hover:bg-gray-600 rounded transition-colors"
                      title="編輯"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => deleteStrategy(strategy.id)}
                      className="p-1 text-gray-400 hover:text-red-400 hover:bg-gray-600 rounded transition-colors"
                      title="刪除"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 統計信息 */}
      <div className="mt-6 grid grid-cols-4 gap-4">
        <div className="bg-gray-700 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-blue-400">{strategies.length}</div>
          <div className="text-xs text-gray-400">總策略數</div>
        </div>
        <div className="bg-gray-700 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-green-400">
            {strategies.filter(s => s.enabled).length}
          </div>
          <div className="text-xs text-gray-400">已啟動</div>
        </div>
        <div className="bg-gray-700 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-gray-400">
            {strategies.filter(s => !s.enabled).length}
          </div>
          <div className="text-xs text-gray-400">未啟動</div>
        </div>
        <div className="bg-gray-700 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-purple-400">
            {strategies.filter(s => s.frequency === '即時').length}
          </div>
          <div className="text-xs text-gray-400">即時策略</div>
        </div>
      </div>
    </div>
  );
};

export default PostMarketSelectionTable;
