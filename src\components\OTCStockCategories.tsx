import React from 'react';

const OTCStockCategories = () => {
  const otcCategories = [
    { code: '類股名稱', time: '時間', index: '指數', change: '漲跌', changePercent: '漲跌%', volume: '成交值', volumePercent: '成交%', avgPrice: '平均價', comparePrice: '比重差%' },
    { code: '新藥醫療', time: '13:35', index: '157.83', change: '▲1.47', changePercent: '+0.94', volume: '3.59', volumePercent: '0.02', avgPrice: '1.73', comparePrice: '+0.00' },
    { code: '新藥議題', time: '13:35', index: '121.73', change: '▼0.24', changePercent: '-0.20', volume: '0.19', volumePercent: '0.02', avgPrice: '0.02', comparePrice: '-0.00' },
    { code: '航運業', time: '13:35', index: '181.60', change: '▲0.16', changePercent: '+0.09', volume: '25.15', volumePercent: '2.20', avgPrice: '1.73', comparePrice: '+0.47' },
    { code: '化工', time: '13:35', index: '147.80', change: '▲5.02', changePercent: '+3.51', volume: '19.36', volumePercent: '1.69', avgPrice: '0.78', comparePrice: '+0.91' },
    { code: '鋼鐵', time: '13:35', index: '92.97', change: '▼0.30', changePercent: '-0.32', volume: '1.78', volumePercent: '0.16', avgPrice: '0.13', comparePrice: '+0.00' },
    { code: '電子', time: '13:35', index: '376.79', change: '▲4.04', changePercent: '+1.08', volume: '938.53', volumePercent: '82.12', avgPrice: '77.13', comparePrice: '+4.99' },
    { code: '建材營造', time: '13:35', index: '134.56', change: '▲2.78', changePercent: '+2.11', volume: '15.67', volumePercent: '1.37', avgPrice: '45.23', comparePrice: '+0.28' },
    { code: '食品工業', time: '13:35', index: '167.89', change: '▲1.23', changePercent: '+0.74', volume: '8.45', volumePercent: '0.74', avgPrice: '56.78', comparePrice: '+0.15' },
    { code: '紡織纖維', time: '13:35', index: '89.34', change: '▼0.67', changePercent: '-0.74', volume: '4.23', volumePercent: '0.37', avgPrice: '23.45', comparePrice: '-0.05' },
    { code: '電機機械', time: '13:35', index: '198.76', change: '▲3.45', changePercent: '+1.77', volume: '23.89', volumePercent: '2.09', avgPrice: '78.90', comparePrice: '+0.34' },
    { code: '玻璃陶瓷', time: '13:35', index: '112.45', change: '▲0.89', changePercent: '+0.80', volume: '2.34', volumePercent: '0.20', avgPrice: '34.56', comparePrice: '+0.08' },
    { code: '造紙工業', time: '13:35', index: '78.90', change: '▼0.45', changePercent: '-0.57', volume: '1.67', volumePercent: '0.15', avgPrice: '19.78', comparePrice: '-0.03' },
    { code: '橡膠工業', time: '13:35', index: '145.67', change: '▲2.34', changePercent: '+1.63', volume: '6.78', volumePercent: '0.59', avgPrice: '45.23', comparePrice: '+0.18' },
    { code: '汽車工業', time: '13:35', index: '156.78', change: '▲4.56', changePercent: '+3.00', volume: '12.34', volumePercent: '1.08', avgPrice: '67.89', comparePrice: '+0.45' },
    { code: '觀光事業', time: '13:35', index: '87.65', change: '▼1.23', changePercent: '-1.38', volume: '3.45', volumePercent: '0.30', avgPrice: '28.90', comparePrice: '-0.12' },
    { code: '金融保險', time: '13:35', index: '134.56', change: '▲1.78', changePercent: '+1.34', volume: '45.67', volumePercent: '4.00', avgPrice: '34.56', comparePrice: '+0.56' },
    { code: '貿易百貨', time: '13:35', index: '123.45', change: '▲0.89', changePercent: '+0.73', volume: '8.90', volumePercent: '0.78', avgPrice: '45.67', comparePrice: '+0.12' },
    { code: '文化創意', time: '13:35', index: '98.76', change: '▲1.45', changePercent: '+1.49', volume: '5.67', volumePercent: '0.50', avgPrice: '23.45', comparePrice: '+0.18' },
    { code: '農業科技', time: '13:35', index: '167.89', change: '▲2.34', changePercent: '+1.41', volume: '7.89', volumePercent: '0.69', avgPrice: '56.78', comparePrice: '+0.23' },
    { code: '其他', time: '13:35', index: '145.23', change: '▲1.67', changePercent: '+1.16', volume: '23.45', volumePercent: '2.05', avgPrice: '67.89', comparePrice: '+0.34' },
  ];

  return (
    <div className="h-full bg-gray-900">
      <div className="bg-gray-800 px-3 py-2 border-b border-gray-700">
        <h3 className="text-white text-sm font-semibold">上櫃類股</h3>
      </div>
      <div className="overflow-auto h-full custom-scrollbar">
        <table className="w-full text-xs">
          <thead className="bg-gray-800 sticky top-0">
            <tr className="text-gray-300">
              <th className="px-2 py-1 text-left border-r border-gray-700">類股名稱</th>
              <th className="px-2 py-1 text-center border-r border-gray-700">時間</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">指數</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">漲跌</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">漲跌%</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">成交值</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">成交%</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">平均價</th>
              <th className="px-2 py-1 text-right">比重差%</th>
            </tr>
          </thead>
          <tbody>
            {otcCategories.slice(1).map((category, index) => (
              <tr key={index} className="hover:bg-gray-800 border-b border-gray-800">
                <td className="px-2 py-1 text-white border-r border-gray-700">{category.code}</td>
                <td className="px-2 py-1 text-gray-300 text-center border-r border-gray-700">{category.time}</td>
                <td className={`px-2 py-1 text-right border-r border-gray-700 ${
                  category.change.includes('▲') ? 'text-red-400' : 
                  category.change.includes('▼') ? 'text-green-400' : 'text-white'
                }`}>{category.index}</td>
                <td className={`px-2 py-1 text-right border-r border-gray-700 ${
                  category.change.includes('▲') ? 'text-red-400' : 
                  category.change.includes('▼') ? 'text-green-400' : 'text-white'
                }`}>{category.change}</td>
                <td className={`px-2 py-1 text-right border-r border-gray-700 ${
                  category.changePercent.includes('+') ? 'text-red-400' : 
                  category.changePercent.includes('-') ? 'text-green-400' : 'text-white'
                }`}>{category.changePercent}</td>
                <td className="px-2 py-1 text-white text-right border-r border-gray-700">{category.volume}</td>
                <td className="px-2 py-1 text-white text-right border-r border-gray-700">{category.volumePercent}</td>
                <td className="px-2 py-1 text-white text-right border-r border-gray-700">{category.avgPrice}</td>
                <td className={`px-2 py-1 text-right ${
                  category.comparePrice.includes('+') ? 'text-red-400' : 
                  category.comparePrice.includes('-') ? 'text-green-400' : 'text-white'
                }`}>{category.comparePrice}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default OTCStockCategories;