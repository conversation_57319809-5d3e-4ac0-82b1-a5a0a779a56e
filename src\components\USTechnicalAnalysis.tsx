import React from 'react';

const USTechnicalAnalysis = () => {
  const indicators = [
    { name: 'RSI(14)', value: '58.32', signal: 'Neutral', color: 'text-yellow-400' },
    { name: 'MACD', value: '2.45', signal: 'Buy', color: 'text-green-400' },
    { name: 'Stochastic', value: '72.8', signal: 'Sell', color: 'text-red-400' },
    { name: 'SMA20', value: '423.15', signal: 'Rising', color: 'text-green-400' },
    { name: 'SMA50', value: '418.90', signal: 'Rising', color: 'text-green-400' },
    { name: 'SMA200', value: '405.30', signal: 'Rising', color: 'text-green-400' },
    { name: 'BB Upper', value: '435.80', signal: 'Resistance', color: 'text-red-400' },
    { name: 'BB Lower', value: '419.40', signal: 'Support', color: 'text-green-400' },
  ];

  const orderBook = [
    { price: '426.25', volume: '2,500', type: 'sell' },
    { price: '426.00', volume: '3,200', type: 'sell' },
    { price: '425.75', volume: '1,800', type: 'sell' },
    { price: '425.67', volume: '0', type: 'current' },
    { price: '425.50', volume: '2,100', type: 'buy' },
    { price: '425.25', volume: '2,900', type: 'buy' },
    { price: '425.00', volume: '1,600', type: 'buy' },
  ];

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* 標題 */}
      <div className="bg-gray-800 px-3 py-2 border-b border-gray-700">
        <h3 className="text-white text-sm font-semibold">Technical Analysis</h3>
      </div>

      {/* 技術指標 */}
      <div className="flex-1 p-3 overflow-auto custom-scrollbar">
        <div className="mb-4">
          <h4 className="text-gray-300 text-xs font-medium mb-2">Technical Indicators</h4>
          <div className="space-y-2">
            {indicators.map((indicator, index) => (
              <div key={index} className="flex items-center justify-between text-xs">
                <span className="text-gray-400">{indicator.name}</span>
                <div className="flex items-center space-x-2">
                  <span className="text-white">{indicator.value}</span>
                  <span className={`${indicator.color} font-medium`}>{indicator.signal}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Level II 報價 */}
        <div>
          <h4 className="text-gray-300 text-xs font-medium mb-2">Level II Quotes</h4>
          <div className="space-y-1">
            {orderBook.map((order, index) => (
              <div 
                key={index} 
                className={`flex items-center justify-between text-xs py-1 px-2 rounded ${
                  order.type === 'current' 
                    ? 'bg-blue-900/30 border border-blue-600' 
                    : order.type === 'sell' 
                    ? 'bg-red-900/10' 
                    : 'bg-green-900/10'
                }`}
              >
                <span className={`font-medium ${
                  order.type === 'current' 
                    ? 'text-blue-400' 
                    : order.type === 'sell' 
                    ? 'text-red-400' 
                    : 'text-green-400'
                }`}>
                  ${order.price}
                </span>
                <span className="text-gray-300">{order.volume}</span>
              </div>
            ))}
          </div>
        </div>

        {/* 成交明細 */}
        <div className="mt-4">
          <h4 className="text-gray-300 text-xs font-medium mb-2">Time & Sales</h4>
          <div className="space-y-1 text-xs">
            <div className="flex justify-between text-gray-400">
              <span>Time</span>
              <span>Price</span>
              <span>Size</span>
            </div>
            {[
              { time: '16:00:12', price: '425.75', volume: '500', type: 'buy' },
              { time: '16:00:08', price: '425.50', volume: '300', type: 'sell' },
              { time: '16:00:05', price: '425.67', volume: '800', type: 'buy' },
              { time: '16:00:01', price: '425.25', volume: '250', type: 'sell' },
            ].map((trade, index) => (
              <div key={index} className="flex justify-between">
                <span className="text-gray-400">{trade.time}</span>
                <span className={trade.type === 'buy' ? 'text-green-400' : 'text-red-400'}>
                  ${trade.price}
                </span>
                <span className="text-white">{trade.volume}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default USTechnicalAnalysis;