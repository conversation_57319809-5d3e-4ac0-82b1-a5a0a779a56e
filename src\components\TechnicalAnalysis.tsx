import React from 'react';

const TechnicalAnalysis = () => {
  const indicators = [
    { name: 'RSI(14)', value: '68.45', signal: '中性', color: 'text-yellow-400' },
    { name: 'MACD', value: '1.23', signal: '買進', color: 'text-green-400' },
    { name: 'KD', value: '75.2', signal: '賣出', color: 'text-red-400' },
    { name: 'MA5', value: '232.15', signal: '上升', color: 'text-green-400' },
    { name: 'MA20', value: '228.90', signal: '上升', color: 'text-green-400' },
    { name: 'MA60', value: '225.30', signal: '上升', color: 'text-green-400' },
    { name: 'BOLL上', value: '235.80', signal: '壓力', color: 'text-red-400' },
    { name: 'BOLL下', value: '229.40', signal: '支撐', color: 'text-green-400' },
  ];

  const orderBook = [
    { price: '234.5', volume: '150', type: 'sell' },
    { price: '234.0', volume: '200', type: 'sell' },
    { price: '233.5', volume: '180', type: 'sell' },
    { price: '233.17', volume: '0', type: 'current' },
    { price: '233.0', volume: '220', type: 'buy' },
    { price: '232.5', volume: '190', type: 'buy' },
    { price: '232.0', volume: '160', type: 'buy' },
  ];

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* 標題 */}
      <div className="bg-gray-800 px-3 py-2 border-b border-gray-700">
        <h3 className="text-white text-sm font-semibold">技術分析</h3>
      </div>

      {/* 技術指標 */}
      <div className="flex-1 p-3 overflow-auto custom-scrollbar table-container">
        <div className="mb-4">
          <h4 className="text-gray-300 text-xs font-medium mb-2">技術指標</h4>
          <div className="space-y-2">
            {indicators.map((indicator, index) => (
              <div key={index} className="flex items-center justify-between text-xs">
                <span className="text-gray-400">{indicator.name}</span>
                <div className="flex items-center space-x-2">
                  <span className="text-white">{indicator.value}</span>
                  <span className={`${indicator.color} font-medium`}>{indicator.signal}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 五檔報價 */}
        <div>
          <h4 className="text-gray-300 text-xs font-medium mb-2">五檔報價</h4>
          <div className="space-y-1">
            {orderBook.map((order, index) => (
              <div 
                key={index} 
                className={`flex items-center justify-between text-xs py-1 px-2 rounded ${
                  order.type === 'current' 
                    ? 'bg-blue-900/30 border border-blue-600' 
                    : order.type === 'sell' 
                    ? 'bg-red-900/10' 
                    : 'bg-green-900/10'
                }`}
              >
                <span className={`font-medium ${
                  order.type === 'current' 
                    ? 'text-blue-400' 
                    : order.type === 'sell' 
                    ? 'text-red-400' 
                    : 'text-green-400'
                }`}>
                  {order.price}
                </span>
                <span className="text-gray-300">{order.volume}</span>
              </div>
            ))}
          </div>
        </div>

        {/* 成交明細 */}
        <div className="mt-4">
          <h4 className="text-gray-300 text-xs font-medium mb-2">成交明細</h4>
          <div className="space-y-1 text-xs">
            <div className="flex justify-between text-gray-400">
              <span>時間</span>
              <span>價格</span>
              <span>量</span>
            </div>
            {[
              { time: '13:35:12', price: '233.5', volume: '50', type: 'buy' },
              { time: '13:35:08', price: '233.0', volume: '30', type: 'sell' },
              { time: '13:35:05', price: '233.2', volume: '80', type: 'buy' },
              { time: '13:35:01', price: '232.8', volume: '25', type: 'sell' },
            ].map((trade, index) => (
              <div key={index} className="flex justify-between">
                <span className="text-gray-400">{trade.time}</span>
                <span className={trade.type === 'buy' ? 'text-red-400' : 'text-green-400'}>
                  {trade.price}
                </span>
                <span className="text-white">{trade.volume}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TechnicalAnalysis;