import React, { useState } from 'react';
import TopToolbar from './TopToolbar';
import TaiwanStockPageWithSplitPane from './TaiwanStockPageWithSplitPane';
import USStockPage from './USStockPage';
import WatchlistPage from './WatchlistPage';
import BlankHomePage from './BlankHomePage';
import StrategySettingsPage from './StrategySettingsPage';
import ExecutionStatusPage from './ExecutionStatusPage';
import StreamControlPanel from './StreamControlPanel';
import { PanelProvider } from '../contexts/PanelContext';
import { WindowStreamProvider } from '../contexts/WindowStreamContext';

const TradingInterface = () => {
  // 預設顯示台股大盤頁面，而不是空白首頁
  const [currentMarket, setCurrentMarket] = useState<'taiwan' | 'us'>('taiwan');
  const [currentPage, setCurrentPage] = useState<'home' | 'market' | 'watchlist' | 'portfolio' | 'strategy' | 'strategy-settings' | 'execution-status'>('market');

  const handleMarketChange = (market: 'taiwan' | 'us') => {
    setCurrentMarket(market);
    // 當切換市場時，自動切換到大盤頁面
    setCurrentPage('market');
  };

  const handlePageChange = (page: 'home' | 'market' | 'watchlist' | 'portfolio' | 'strategy' | 'strategy-settings' | 'execution-status') => {
    setCurrentPage(page);
  };

  // 渲染當前頁面內容
  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'home':
        return <BlankHomePage />;
      case 'market':
        // 使用新的 SplitPane 版本的台股頁面
        return currentMarket === 'taiwan' ? <TaiwanStockPageWithSplitPane /> : <USStockPage />;
      case 'watchlist':
        return <WatchlistPage />;
      case 'portfolio':
        return (
          <div className="flex-1 flex items-center justify-center bg-gray-900">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-white mb-4">庫存股</h2>
              <p className="text-gray-400">庫存股功能開發中...</p>
            </div>
          </div>
        );
      case 'strategy':
        return (
          <div className="flex-1 flex items-center justify-center bg-gray-900">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-white mb-4">策略設定</h2>
              <p className="text-gray-400">策略設定功能開發中...</p>
            </div>
          </div>
        );
      case 'strategy-settings':
        return <StrategySettingsPage />;
      case 'execution-status':
        return <ExecutionStatusPage />;
      default:
        return currentMarket === 'taiwan' ? <TaiwanStockPageWithSplitPane /> : <USStockPage />;
    }
  };

  return (
    <WindowStreamProvider>
      <div className="h-screen flex flex-col bg-gray-900" data-app-id="trading-interface">
        <TopToolbar 
          currentMarket={currentMarket} 
          onMarketChange={handleMarketChange}
          currentPage={currentPage}
          onPageChange={handlePageChange}
        />
        <PanelProvider market={currentMarket}>
          {renderCurrentPage()}
        </PanelProvider>
        
        {/* 全域數據流控制面板 */}
        <StreamControlPanel />
      </div>
    </WindowStreamProvider>
  );
};

export default TradingInterface;