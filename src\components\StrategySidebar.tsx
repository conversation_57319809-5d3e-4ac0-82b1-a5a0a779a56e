import React from 'react';
import { Settings, Activity } from 'lucide-react';

interface StrategySidebarProps {
  onCategorySelect: (category: string) => void;
  selectedCategory: string;
}

const StrategySidebar: React.FC<StrategySidebarProps> = ({
  onCategorySelect,
  selectedCategory
}) => {
  const menuItems = [
    {
      id: 'strategySettings',
      label: '策略設定',
      icon: Settings,
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'executionStatus',
      label: '執行狀態',
      icon: Activity,
      color: 'from-green-500 to-green-600'
    }
  ];

  return (
    <div className="w-8 bg-gradient-to-b from-gray-100 to-gray-200 border-r border-gray-300 flex flex-col">
      {/* 垂直選單項目 */}
      <div className="flex-1 py-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isSelected = selectedCategory === item.id;
          
          return (
            <button
              key={item.id}
              onClick={() => onCategorySelect(item.id)}
              tabIndex={-1}
              className={`w-full h-20 flex flex-col items-center justify-center text-xs transition-all duration-200 hover:bg-gray-200 relative group ${
                isSelected
                  ? 'bg-gradient-to-b from-blue-100 to-purple-100 border-r-4 border-blue-500 shadow-lg'
                  : ''
              }`}
            >
              <div className={`w-6 h-6 bg-gradient-to-r ${
                isSelected ? 'from-blue-600 to-purple-600 shadow-lg scale-110' : item.color
              } rounded-md flex items-center justify-center shadow-sm mb-1 transition-all duration-200`}>
                <Icon className="h-3 w-3 text-white" />
              </div>
              
              {/* 垂直文字 */}
              <div className="writing-mode-vertical text-vertical-rl">
                <span className={`font-medium text-xs transition-colors duration-200 ${
                  isSelected ? 'text-blue-800 font-bold' : 'text-gray-700'
                }`}>
                  {item.label}
                </span>
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default StrategySidebar;
