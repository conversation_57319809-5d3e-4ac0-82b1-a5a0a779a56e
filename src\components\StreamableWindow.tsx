import React, { useState, useEffect } from 'react';
import { Play, Pause, RefreshCw, Settings, Wifi, WifiOff } from 'lucide-react';
import { useWindowData } from '../hooks/useWindowData';

interface StreamableWindowProps {
  windowId: string;
  type: 'chart' | 'list' | 'market' | 'technical';
  title: string;
  children: React.ReactNode;
  updateInterval?: number;
  priority?: 'high' | 'medium' | 'low';
  showControls?: boolean;
  className?: string;
}

const StreamableWindow: React.FC<StreamableWindowProps> = ({
  windowId,
  type,
  title,
  children,
  updateInterval = 1000,
  priority = 'medium',
  showControls = true,
  className = ''
}) => {
  const {
    data,
    isLoading,
    error,
    lastUpdate,
    isStreamEnabled,
    streamPriority,
    manualUpdate,
    toggleStream
  } = useWindowData({
    windowId,
    type,
    updateInterval,
    priority
  });

  const [showSettings, setShowSettings] = useState(false);

  // 格式化最後更新時間
  const formatLastUpdate = (timestamp: number) => {
    if (!timestamp) return '未更新';
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 1000) return '剛剛';
    if (diff < 60000) return `${Math.floor(diff / 1000)}秒前`;
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分鐘前`;
    return new Date(timestamp).toLocaleTimeString();
  };

  // 獲取優先級顏色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-400';
      case 'low': return 'text-green-400';
      default: return 'text-yellow-400';
    }
  };

  return (
    <div className={`h-full bg-gray-900 border border-gray-700 rounded-lg overflow-hidden ${className}`}>
      {/* 視窗標題列 */}
      <div className="bg-gray-800 px-3 py-2 border-b border-gray-700 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h3 className="text-white text-sm font-semibold">{title}</h3>
          
          {/* 連線狀態指示器 */}
          <div className="flex items-center space-x-1">
            {isStreamEnabled ? (
              <Wifi className="w-3 h-3 text-green-400" />
            ) : (
              <WifiOff className="w-3 h-3 text-gray-500" />
            )}
            <span className={`text-xs ${getPriorityColor(streamPriority)}`}>
              {streamPriority.toUpperCase()}
            </span>
          </div>

          {/* 載入指示器 */}
          {isLoading && (
            <RefreshCw className="w-3 h-3 text-blue-400 animate-spin" />
          )}

          {/* 錯誤指示器 */}
          {error && (
            <div className="w-2 h-2 bg-red-500 rounded-full" title={error} />
          )}
        </div>

        {/* 控制按鈕 */}
        {showControls && (
          <div className="flex items-center space-x-1">
            {/* 最後更新時間 */}
            <span className="text-xs text-gray-400 mr-2">
              {formatLastUpdate(lastUpdate)}
            </span>

            {/* 手動更新 */}
            <button
              onClick={manualUpdate}
              disabled={isLoading}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50"
              title="手動更新"
            >
              <RefreshCw className={`w-3 h-3 ${isLoading ? 'animate-spin' : ''}`} />
            </button>

            {/* 暫停/恢復 */}
            <button
              onClick={toggleStream}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title={isStreamEnabled ? '暫停更新' : '恢復更新'}
            >
              {isStreamEnabled ? (
                <Pause className="w-3 h-3" />
              ) : (
                <Play className="w-3 h-3" />
              )}
            </button>

            {/* 設定 */}
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title="設定"
            >
              <Settings className="w-3 h-3" />
            </button>
          </div>
        )}
      </div>

      {/* 設定面板 */}
      {showSettings && (
        <div className="bg-gray-800 px-3 py-2 border-b border-gray-700">
          <div className="text-xs text-gray-300 space-y-1">
            <div>視窗ID: {windowId}</div>
            <div>類型: {type}</div>
            <div>更新間隔: {updateInterval}ms</div>
            <div>優先級: {streamPriority}</div>
            <div>狀態: {isStreamEnabled ? '啟用' : '停用'}</div>
          </div>
        </div>
      )}

      {/* 視窗內容 */}
      <div className="h-full overflow-hidden">
        {children}
      </div>
    </div>
  );
};

export default StreamableWindow;