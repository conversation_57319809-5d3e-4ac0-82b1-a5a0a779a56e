import React from 'react';

const StockFutures = () => {
  const futuresData = [
    { code: '商品名稱', time: '時間', price: '成交價', change: '漲跌', changePercent: '漲跌%', volume: '成交量', openInterest: '未平倉', high: '最高', low: '最低' },
    { code: '台指期', time: '13:35', price: '17,245', change: '▲125', changePercent: '+0.73', volume: '89,567', openInterest: '234,567', high: '17,289', low: '17,156' },
    { code: '小台指', time: '13:35', price: '17,248', change: '▲128', changePercent: '+0.75', volume: '156,789', openInterest: '456,789', high: '17,292', low: '17,159' },
    { code: '電子期', time: '13:35', price: '789.5', change: '▲12.3', changePercent: '+1.58', volume: '23,456', openInterest: '67,890', high: '795.2', low: '782.1' },
    { code: '金融期', time: '13:35', price: '1,456.7', change: '▲8.9', changePercent: '+0.62', volume: '12,345', openInterest: '34,567', high: '1,462.3', low: '1,448.9' },
    { code: '台指選擇權', time: '13:35', price: '156.5', change: '▲3.2', changePercent: '+2.09', volume: '45,678', openInterest: '123,456', high: '159.8', low: '152.3' },
    { code: '2330期', time: '13:35', price: '567.0', change: '▲8.5', changePercent: '+1.52', volume: '8,901', openInterest: '23,456', high: '572.5', low: '561.0' },
    { code: '2317期', time: '13:35', price: '89.7', change: '▲1.2', changePercent: '+1.36', volume: '5,678', openInterest: '12,345', high: '90.5', low: '88.9' },
    { code: '2454期', time: '13:35', price: '234.5', change: '▲4.8', changePercent: '+2.09', volume: '3,456', openInterest: '8,901', high: '237.2', low: '231.8' },
    { code: '2881期', time: '13:35', price: '23.45', change: '▲0.15', changePercent: '+0.64', volume: '12,345', openInterest: '34,567', high: '23.58', low: '23.32' },
    { code: '2882期', time: '13:35', price: '15.67', change: '▲0.08', changePercent: '+0.51', volume: '8,901', openInterest: '23,456', high: '15.72', low: '15.61' },
    { code: '2886期', time: '13:35', price: '34.56', change: '▲0.23', changePercent: '+0.67', volume: '6,789', openInterest: '18,901', high: '34.78', low: '34.45' },
    { code: '2891期', time: '13:35', price: '12.34', change: '▲0.05', changePercent: '+0.41', volume: '4,567', openInterest: '12,345', high: '12.38', low: '12.29' },
    { code: '2892期', time: '13:35', price: '45.67', change: '▲0.34', changePercent: '+0.75', volume: '7,890', openInterest: '21,234', high: '45.89', low: '45.45' },
    { code: '6505期', time: '13:35', price: '678.9', change: '▲12.5', changePercent: '+1.88', volume: '2,345', openInterest: '6,789', high: '685.2', low: '672.3' },
    { code: '2303期', time: '13:35', price: '456.7', change: '▲7.8', changePercent: '+1.74', volume: '3,456', openInterest: '9,012', high: '461.2', low: '451.8' },
    { code: '2308期', time: '13:35', price: '89.45', change: '▲1.23', changePercent: '+1.39', volume: '5,678', openInterest: '15,678', high: '90.12', low: '88.67' },
    { code: '2382期', time: '13:35', price: '123.4', change: '▲2.1', changePercent: '+1.73', volume: '4,567', openInterest: '12,890', high: '125.2', low: '121.8' },
    { code: '2412期', time: '13:35', price: '67.89', change: '▲0.89', changePercent: '+1.33', volume: '6,789', openInterest: '18,234', high: '68.45', low: '67.23' },
    { code: '2474期', time: '13:35', price: '234.5', change: '▲3.4', changePercent: '+1.47', volume: '2,345', openInterest: '7,890', high: '236.8', low: '232.1' },
    { code: '3008期', time: '13:35', price: '45.67', change: '▲0.56', changePercent: '+1.24', volume: '8,901', openInterest: '23,456', high: '46.12', low: '45.23' },
  ];

  return (
    <div className="h-full bg-gray-900">
      <div className="bg-gray-800 px-3 py-2 border-b border-gray-700">
        <h3 className="text-white text-sm font-semibold">股票期貨</h3>
      </div>
      <div className="overflow-auto h-full custom-scrollbar">
        <table className="w-full text-xs">
          <thead className="bg-gray-800 sticky top-0">
            <tr className="text-gray-300">
              <th className="px-2 py-1 text-left border-r border-gray-700">商品名稱</th>
              <th className="px-2 py-1 text-center border-r border-gray-700">時間</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">成交價</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">漲跌</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">漲跌%</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">成交量</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">未平倉</th>
              <th className="px-2 py-1 text-right border-r border-gray-700">最高</th>
              <th className="px-2 py-1 text-right">最低</th>
            </tr>
          </thead>
          <tbody>
            {futuresData.slice(1).map((futures, index) => (
              <tr key={index} className="hover:bg-gray-800 border-b border-gray-800">
                <td className="px-2 py-1 text-white border-r border-gray-700 font-medium">{futures.code}</td>
                <td className="px-2 py-1 text-gray-300 text-center border-r border-gray-700">{futures.time}</td>
                <td className={`px-2 py-1 text-right border-r border-gray-700 font-medium ${
                  futures.change.includes('▲') ? 'text-red-400' : 
                  futures.change.includes('▼') ? 'text-green-400' : 'text-white'
                }`}>{futures.price}</td>
                <td className={`px-2 py-1 text-right border-r border-gray-700 ${
                  futures.change.includes('▲') ? 'text-red-400' : 
                  futures.change.includes('▼') ? 'text-green-400' : 'text-white'
                }`}>{futures.change}</td>
                <td className={`px-2 py-1 text-right border-r border-gray-700 ${
                  futures.changePercent.includes('+') ? 'text-red-400' : 
                  futures.changePercent.includes('-') ? 'text-green-400' : 'text-white'
                }`}>{futures.changePercent}</td>
                <td className="px-2 py-1 text-white text-right border-r border-gray-700">{futures.volume}</td>
                <td className="px-2 py-1 text-white text-right border-r border-gray-700">{futures.openInterest}</td>
                <td className="px-2 py-1 text-white text-right border-r border-gray-700">{futures.high}</td>
                <td className="px-2 py-1 text-white text-right">{futures.low}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default StockFutures;