import React, { useState, useRef, useEffect } from 'react';
import { Edit, Trash2, Play, Square, BarChart3, Clock, XCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels';
import StrategySidebar from './StrategySidebar';

// 策略項目定義
interface StrategyItem {
  id: number;
  enabled: boolean;
  autoExecute: boolean; // 自動執行狀態
  strategyCode: string;
  strategyName: string;
  tradingProduct: string;
  direction: '多頭' | '空頭' | '雙向';
  frequency: string;
  activationTime: string;
}

// 初始策略數據 - 每個類型3個策略
const initialStrategies = {
  postMarket: [
    { id: 1, enabled: true, autoExecute: false, strategyCode: 'PMS001', strategyName: '均線突破選股', tradingProduct: '台股', direction: '多頭' as const, frequency: '每日', activationTime: '09:00' },
    { id: 2, enabled: false, autoExecute: true, strategyCode: 'PMS002', strategyName: 'RSI超賣選股', tradingProduct: '台股', direction: '多頭' as const, frequency: '每日', activationTime: '09:30' },
    { id: 3, enabled: true, autoExecute: false, strategyCode: 'PMS003', strategyName: '布林通道突破', tradingProduct: '台股', direction: '雙向' as const, frequency: '每週', activationTime: '10:00' }
  ],
  alert: [
    { id: 4, enabled: true, autoExecute: false, strategyCode: 'ALT001', strategyName: '價格突破警示', tradingProduct: '台股', direction: '雙向' as const, frequency: '即時', activationTime: '09:00' },
    { id: 5, enabled: false, autoExecute: true, strategyCode: 'ALT002', strategyName: 'RSI超買超賣警示', tradingProduct: '台股', direction: '雙向' as const, frequency: '即時', activationTime: '09:00' },
    { id: 6, enabled: true, autoExecute: false, strategyCode: 'ALT003', strategyName: '成交量異常警示', tradingProduct: '台股', direction: '雙向' as const, frequency: '即時', activationTime: '09:00' }
  ],
  trading: [
    { id: 7, enabled: true, autoExecute: false, strategyCode: 'TRD001', strategyName: '動量交易策略', tradingProduct: '台股', direction: '多頭' as const, frequency: '即時', activationTime: '09:00' },
    { id: 8, enabled: false, autoExecute: true, strategyCode: 'TRD002', strategyName: '均值回歸策略', tradingProduct: '台股', direction: '空頭' as const, frequency: '每小時', activationTime: '09:30' },
    { id: 9, enabled: true, autoExecute: false, strategyCode: 'TRD003', strategyName: '網格交易策略', tradingProduct: '台股', direction: '雙向' as const, frequency: '即時', activationTime: '10:00' }
  ]
};

const StrategySettingsPage: React.FC = () => {
  // 策略狀態管理
  const [postMarketStrategies, setPostMarketStrategies] = useState<StrategyItem[]>(initialStrategies.postMarket);
  const [alertStrategies, setAlertStrategies] = useState<StrategyItem[]>(initialStrategies.alert);
  const [tradingStrategies, setTradingStrategies] = useState<StrategyItem[]>(initialStrategies.trading);

  // 選中的盤後選股策略
  const [selectedPostMarketStrategy, setSelectedPostMarketStrategy] = useState<number | null>(null);

  // 選中的盤中警示策略
  const [selectedAlertStrategy, setSelectedAlertStrategy] = useState<number | null>(null);

  // 自選股股池篩選類型
  const [selectedStockPoolType, setSelectedStockPoolType] = useState<string>('日內當沖');

  // 當前選中的導航頁面
  const [currentNavPage, setCurrentNavPage] = useState<string>('策略管理');

  // 當前顯示的頁面內容
  const [currentPageContent, setCurrentPageContent] = useState<string>('策略設定');

  // 實盤監測子選單顯示狀態
  const [showLiveMonitorSubmenu, setShowLiveMonitorSubmenu] = useState<boolean>(false);

  // 策略側邊選單選中狀態
  const [selectedStrategyCategory, setSelectedStrategyCategory] = useState<string>('strategySettings');



  // 策略篩選結果中選中的股票
  const [selectedStocks, setSelectedStocks] = useState<Set<number>>(new Set());

  // 顯示加入自選股類型選擇彈跳視窗
  const [showAddToPoolModal, setShowAddToPoolModal] = useState<boolean>(false);

  // 自選股股池選中狀態
  const [selectedPoolStocks, setSelectedPoolStocks] = useState<Set<string>>(new Set());

  // 自選股股池數據
  const [stockPool, setStockPool] = useState<Array<{
    code: string;
    name: string;
    type: string;
    price: number;
    change: number;
    volume: string;
    addedDate: string;
    note: string;
  }>>([
    // 初始示例數據
    { code: '2330', name: '台積電', type: '日內當沖', price: 580.0, change: 2.15, volume: '25,847', addedDate: '2025/01/15', note: '高流動性' },
    { code: '2454', name: '聯發科', type: '日波段', price: 920.0, change: -1.25, volume: '8,456', addedDate: '2025/01/14', note: '技術突破' },
    { code: '2317', name: '鴻海', type: '週波段', price: 105.5, change: 0.95, volume: '45,123', addedDate: '2025/01/13', note: '趨勢向上' },
    { code: '2412', name: '中華電', type: '月波段', price: 123.0, change: 0.00, volume: '12,789', addedDate: '2025/01/12', note: '穩定收息' },
    { code: '1301', name: '台塑', type: '日內當沖', price: 87.2, change: -0.68, volume: '18,654', addedDate: '2025/01/11', note: '波動大' },
    { code: '2881', name: '富邦金', type: '日波段', price: 68.5, change: 1.48, volume: '32,156', addedDate: '2025/01/10', note: '金融股' },
    { code: '3008', name: '大立光', type: '週波段', price: 2450.0, change: -0.81, volume: '1,234', addedDate: '2025/01/09', note: '光學龍頭' },
    { code: '2382', name: '廣達', type: '月波段', price: 245.5, change: 3.21, volume: '15,789', addedDate: '2025/01/08', note: 'AI概念' }
  ]);

  // 策略篩選結果顯示狀態
  const [showStrategyResults, setShowStrategyResults] = useState<boolean>(false);
  const [currentStrategyName, setCurrentStrategyName] = useState<string>('');

  // 日期狀態管理
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);

  // 面板大小狀態管理
  const [leftPanelSize, setLeftPanelSize] = useState<number>(() => {
    // 從 localStorage 讀取保存的面板大小，預設為 70
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('strategy-panel-size');
      return saved ? parseInt(saved, 10) : 70;
    }
    return 70;
  });

  // 欄位設定狀態管理
  const [showColumnSettings, setShowColumnSettings] = useState<boolean>(false);
  const strategyResultsRef = useRef<HTMLDivElement>(null);

  // 可用的欄位定義
  const availableColumns = [
    { id: 'select', name: '選擇', enabled: true, fixed: true },
    { id: 'index', name: '序號', enabled: true, fixed: true },
    { id: 'code', name: '代碼', enabled: true, fixed: false },
    { id: 'name', name: '商品', enabled: true, fixed: false },
    { id: 'price', name: '成交', enabled: true, fixed: false },
    { id: 'change', name: '漲跌%', enabled: true, fixed: false },
    { id: 'volume', name: '總量', enabled: true, fixed: false },
    { id: 'monthlyGrowth', name: '月營收月增率', enabled: true, fixed: false },
    { id: 'yearlyGrowth', name: '月營收年增率', enabled: true, fixed: false },
    { id: 'marketCap', name: '市值', enabled: false, fixed: false },
    { id: 'pe', name: 'P/E比', enabled: false, fixed: false },
    { id: 'pb', name: 'P/B比', enabled: false, fixed: false },
    { id: 'dividend', name: '股息殖利率', enabled: false, fixed: false },
    { id: 'eps', name: 'EPS', enabled: false, fixed: false },
    { id: 'roe', name: 'ROE', enabled: false, fixed: false }
  ];

  const [columns, setColumns] = useState(availableColumns);

  // 不同日期的股票數據
  const stockDataByDate = {
    '2025-01-15': [
      { index: 1, code: '1268', name: '漢來美食', price: 152.5, change: 0.33, volume: 5, monthlyGrowth: 31.38, yearlyGrowth: 21.44, marketCap: 45.2, pe: 15.6, pb: 1.8, dividend: 3.2, eps: 9.8, roe: 12.5 },
      { index: 2, code: '1315', name: '達新', price: 67, change: 0.6, volume: 5, monthlyGrowth: 47.42, yearlyGrowth: 22, marketCap: 23.1, pe: 12.3, pb: 1.5, dividend: 4.1, eps: 5.4, roe: 8.9 },
      { index: 3, code: '1436', name: '華友聯', price: 121, change: 0.41, volume: 227, monthlyGrowth: 1457.4, yearlyGrowth: 53.58, marketCap: 67.8, pe: 18.9, pb: 2.1, dividend: 2.8, eps: 6.4, roe: 15.2 },
      { index: 4, code: '1439', name: '焦揚', price: 30.35, change: 0.66, volume: 5, monthlyGrowth: 313, yearlyGrowth: 115.69, marketCap: 12.4, pe: 9.7, pb: 1.2, dividend: 5.6, eps: 3.1, roe: 7.8 },
      { index: 5, code: '1467', name: '南緯', price: 8.19, change: -1.09, volume: 46, monthlyGrowth: 29.33, yearlyGrowth: 34.12, marketCap: 8.9, pe: 11.2, pb: 0.9, dividend: 6.2, eps: 0.7, roe: 6.1 },
      { index: 6, code: '1514', name: '亞力', price: 107, change: -4.04, volume: 2953, monthlyGrowth: 88.76, yearlyGrowth: 60.42, marketCap: 89.3, pe: 14.8, pb: 1.7, dividend: 3.9, eps: 7.2, roe: 11.6 },
      { index: 7, code: '1530', name: '亞崴', price: 28.45, change: -3.07, volume: 74, monthlyGrowth: 20.45, yearlyGrowth: 29.1, marketCap: 34.2, pe: 13.5, pb: 1.4, dividend: 4.3, eps: 2.1, roe: 9.2 },
      { index: 8, code: '1535', name: '中宇', price: 56.1, change: -0.18, volume: 21, monthlyGrowth: 30.87, yearlyGrowth: 28.14, marketCap: 45.7, pe: 16.2, pb: 1.9, dividend: 3.7, eps: 3.5, roe: 10.8 }
    ],
    '2025-01-14': [
      { index: 1, code: '2330', name: '台積電', price: 575, change: 1.85, volume: 28456, monthlyGrowth: 11.8, yearlyGrowth: 17.9, marketCap: 14890.2, pe: 18.5, pb: 2.1, dividend: 2.8, eps: 31.1, roe: 24.3 },
      { index: 2, code: '2454', name: '聯發科', price: 935, change: 2.15, volume: 9123, monthlyGrowth: 9.2, yearlyGrowth: 16.1, marketCap: 1489.7, pe: 15.2, pb: 1.9, dividend: 3.2, eps: 61.5, roe: 19.8 },
      { index: 3, code: '2303', name: '聯電', price: 52.3, change: 1.95, volume: 67890, monthlyGrowth: 7.5, yearlyGrowth: 12.8, marketCap: 654.8, pe: 12.8, pb: 1.4, dividend: 4.1, eps: 4.1, roe: 11.2 },
      { index: 4, code: '2308', name: '台達電', price: 385, change: -0.52, volume: 15234, monthlyGrowth: 4.2, yearlyGrowth: 8.7, marketCap: 1001.5, pe: 19.3, pb: 2.3, dividend: 2.9, eps: 19.9, roe: 16.7 },
      { index: 5, code: '2002', name: '中鋼', price: 28.9, change: 0.35, volume: 89567, monthlyGrowth: 2.8, yearlyGrowth: 5.9, marketCap: 1156.4, pe: 14.7, pb: 1.1, dividend: 5.2, eps: 2.0, roe: 7.8 },
      { index: 6, code: '2886', name: '兆豐金', price: 38.2, change: 1.06, volume: 45678, monthlyGrowth: 5.7, yearlyGrowth: 10.3, marketCap: 478.9, pe: 11.9, pb: 1.0, dividend: 4.8, eps: 3.2, roe: 8.9 },
      { index: 7, code: '2357', name: '華碩', price: 485, change: -1.22, volume: 3456, monthlyGrowth: 13.4, yearlyGrowth: 19.8, marketCap: 361.2, pe: 16.8, pb: 2.0, dividend: 3.5, eps: 28.9, roe: 17.2 }
    ],
    '2025-01-13': [
      { index: 1, code: '2330', name: '台積電', price: 572, change: -0.52, volume: 31789, monthlyGrowth: 11.2, yearlyGrowth: 17.1, marketCap: 14812.4, pe: 18.4, pb: 2.1, dividend: 2.8, eps: 31.1, roe: 24.3 },
      { index: 2, code: '2317', name: '鴻海', price: 104, change: 1.46, volume: 52345, monthlyGrowth: 4.8, yearlyGrowth: 9.2, marketCap: 1444.8, pe: 13.2, pb: 1.3, dividend: 4.2, eps: 7.9, roe: 9.8 },
      { index: 3, code: '2891', name: '中信金', price: 25.8, change: 0.78, volume: 78901, monthlyGrowth: 3.5, yearlyGrowth: 7.8, marketCap: 394.2, pe: 10.8, pb: 0.9, dividend: 5.1, eps: 2.4, roe: 8.3 },
      { index: 4, code: '1216', name: '統一', price: 78.5, change: -0.63, volume: 23456, monthlyGrowth: 1.9, yearlyGrowth: 4.2, marketCap: 987.3, pe: 17.5, pb: 1.8, dividend: 3.8, eps: 4.5, roe: 10.2 },
      { index: 5, code: '2395', name: '研華', price: 425, change: 2.41, volume: 8789, monthlyGrowth: 16.7, yearlyGrowth: 24.3, marketCap: 213.4, pe: 22.1, pb: 2.9, dividend: 2.1, eps: 19.2, roe: 18.7 },
      { index: 6, code: '3711', name: '日月光投控', price: 125, change: 0.81, volume: 19234, monthlyGrowth: 8.9, yearlyGrowth: 14.6, marketCap: 543.2, pe: 14.3, pb: 1.6, dividend: 3.9, eps: 8.7, roe: 11.8 }
    ],
    '2025-01-12': [
      { index: 1, code: '2454', name: '聯發科', price: 915, change: 1.67, volume: 12345, monthlyGrowth: 8.9, yearlyGrowth: 15.7, marketCap: 1458.2, pe: 14.9, pb: 1.9, dividend: 3.2, eps: 61.5, roe: 19.8 },
      { index: 2, code: '2412', name: '中華電', price: 122.5, change: -0.41, volume: 18567, monthlyGrowth: 1.8, yearlyGrowth: 4.1, marketCap: 951.7, pe: 16.2, pb: 1.7, dividend: 4.5, eps: 7.6, roe: 10.5 },
      { index: 3, code: '2207', name: '和泰車', price: 685, change: 1.93, volume: 5678, monthlyGrowth: 12.3, yearlyGrowth: 18.9, marketCap: 765.4, pe: 19.8, pb: 2.4, dividend: 2.7, eps: 34.6, roe: 17.5 },
      { index: 4, code: '2884', name: '玉山金', price: 28.7, change: 0.35, volume: 56789, monthlyGrowth: 4.6, yearlyGrowth: 9.1, marketCap: 367.8, pe: 12.1, pb: 1.1, dividend: 4.9, eps: 2.4, roe: 9.1 },
      { index: 5, code: '6505', name: '台塑化', price: 95.2, change: -0.94, volume: 34567, monthlyGrowth: 2.7, yearlyGrowth: 6.4, marketCap: 1198.4, pe: 11.7, pb: 1.2, dividend: 5.8, eps: 8.1, roe: 10.3 },
      { index: 6, code: '2408', name: '南亞科', price: 78.9, change: 2.86, volume: 41234, monthlyGrowth: 19.5, yearlyGrowth: 31.2, marketCap: 486.7, pe: 13.4, pb: 1.5, dividend: 3.7, eps: 5.9, roe: 11.2 }
    ],
    '2025-01-11': [
      { index: 1, code: '2330', name: '台積電', price: 568, change: 0.89, volume: 29876, monthlyGrowth: 10.8, yearlyGrowth: 16.5, marketCap: 14708.8, pe: 18.3, pb: 2.1, dividend: 2.8, eps: 31.1, roe: 24.3 },
      { index: 2, code: '3008', name: '大立光', price: 2420, change: 1.26, volume: 2345, monthlyGrowth: 14.7, yearlyGrowth: 21.8, marketCap: 1694.0, pe: 24.2, pb: 3.1, dividend: 1.9, eps: 100.0, roe: 12.8 },
      { index: 3, code: '2382', name: '廣達', price: 238, change: 2.59, volume: 23456, monthlyGrowth: 17.8, yearlyGrowth: 26.9, marketCap: 498.7, pe: 20.5, pb: 2.7, dividend: 2.4, eps: 11.6, roe: 13.2 },
      { index: 4, code: '2603', name: '長榮', price: 195, change: -1.52, volume: 67890, monthlyGrowth: 25.4, yearlyGrowth: 42.7, marketCap: 2535.0, pe: 8.9, pb: 1.8, dividend: 6.2, eps: 21.9, roe: 20.2 },
      { index: 5, code: '2609', name: '陽明', price: 68.2, change: -2.15, volume: 89123, monthlyGrowth: 22.1, yearlyGrowth: 38.5, marketCap: 851.5, pe: 7.2, pb: 1.4, dividend: 7.1, eps: 9.5, roe: 19.4 }
    ]
  };

  // 根據選擇的日期獲取股票數據
  const getStockDataForDate = (date: Date) => {
    const dateKey = date.toISOString().split('T')[0];
    return stockDataByDate[dateKey] || stockDataByDate['2025-01-15']; // 預設使用最新日期的數據
  };

  // 當前顯示的股票數據
  const stockData = getStockDataForDate(selectedDate);

  // 策略表格的 ref 引用
  const postMarketRef = useRef<HTMLDivElement>(null);
  const alertRef = useRef<HTMLDivElement>(null);
  const tradingRef = useRef<HTMLDivElement>(null);

  // 記錄點擊時的滾動位置
  const clickPositionRef = useRef<number>(0);

  // 日期選擇器的 ref
  const datePickerRef = useRef<HTMLDivElement>(null);

  // 點擊外部關閉日期選擇器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target as Node)) {
        setShowDatePicker(false);
      }
    };

    if (showDatePicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showDatePicker]);

  // 記錄點擊位置的函數
  const recordClickPosition = () => {
    clickPositionRef.current = window.pageYOffset || document.documentElement.scrollTop;
  };

  // 滾動到指定策略表格的中心（基於記錄的點擊位置）
  const scrollToStrategyCenter = (type: 'postMarket' | 'alert' | 'trading') => {
    const ref = type === 'postMarket' ? postMarketRef :
                type === 'alert' ? alertRef : tradingRef;

    if (ref.current) {
      // 使用 requestAnimationFrame 確保在DOM更新完成後執行
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          if (ref.current) {
            // 先恢復到記錄的點擊位置，避免跳動
            window.scrollTo({
              top: clickPositionRef.current,
              behavior: 'auto' // 立即恢復，不使用動畫
            });

            // 然後計算目標位置並平滑滾動
            setTimeout(() => {
              if (ref.current) {
                const rect = ref.current.getBoundingClientRect();
                const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const elementTop = rect.top + currentScrollTop;
                const elementHeight = rect.height;
                const windowHeight = window.innerHeight;

                // 計算要滾動到的位置（元素中心對齊視窗中心）
                const targetScrollTop = elementTop - (windowHeight / 2) + (elementHeight / 2);

                // 平滑滾動到目標位置
                window.scrollTo({
                  top: targetScrollTop,
                  behavior: 'smooth'
                });
              }
            }, 50); // 短暫延遲確保位置恢復完成
          }
        });
      });
    }
  };

  // 策略操作函數
  const toggleStrategy = (type: 'postMarket' | 'alert' | 'trading', id: number) => {
    const setStrategies = type === 'postMarket' ? setPostMarketStrategies :
                         type === 'alert' ? setAlertStrategies : setTradingStrategies;

    setStrategies(prev =>
      prev.map(strategy => {
        if (strategy.id === id) {
          return {
            ...strategy,
            enabled: !strategy.enabled
          };
        }
        return strategy;
      })
    );
  };

  // 添加新策略函數
  const addStrategy = (type: 'postMarket' | 'alert' | 'trading') => {
    // 記錄點擊時的位置
    recordClickPosition();

    const setStrategies = type === 'postMarket' ? setPostMarketStrategies :
                         type === 'alert' ? setAlertStrategies : setTradingStrategies;

    const getStrategies = type === 'postMarket' ? postMarketStrategies :
                         type === 'alert' ? alertStrategies : tradingStrategies;

    const newId = Math.max(...getStrategies.map(s => s.id)) + 1;
    const prefix = type === 'postMarket' ? 'PMS' : type === 'alert' ? 'ALT' : 'TRD';
    const codeNumber = String(getStrategies.length + 1).padStart(3, '0');

    const newStrategy: StrategyItem = {
      id: newId,
      enabled: false,
      strategyCode: `${prefix}${codeNumber}`,
      strategyName: '新策略',
      tradingProduct: '台股',
      direction: '多頭',
      frequency: '每日',
      activationTime: '09:00'
    };

    setStrategies(prev => [...prev, newStrategy]);

    // 滾動到該策略表格的中心
    scrollToStrategyCenter(type);
  };

  // 刪除策略函數
  const deleteStrategy = (type: 'postMarket' | 'alert' | 'trading', id: number) => {
    // 記錄點擊時的位置
    recordClickPosition();

    const getStrategies = type === 'postMarket' ? postMarketStrategies :
                         type === 'alert' ? alertStrategies : tradingStrategies;

    const strategy = getStrategies.find(s => s.id === id);

    if (strategy && window.confirm(`確定要刪除策略「${strategy.strategyName}」嗎？`)) {
      const setStrategies = type === 'postMarket' ? setPostMarketStrategies :
                           type === 'alert' ? setAlertStrategies : setTradingStrategies;

      setStrategies(prev => prev.filter(strategy => strategy.id !== id));

      // 滾動到該策略表格的中心
      scrollToStrategyCenter(type);
    }
  };

  // 策略表格組件
  const StrategyTable: React.FC<{
    title: string;
    strategies: StrategyItem[];
    type: 'postMarket' | 'alert' | 'trading';
    color: string;
    tableRef?: React.RefObject<HTMLDivElement>;
  }> = ({ title, strategies, type, color, tableRef }) => {
    return (
      <div ref={tableRef} className="bg-gray-800 rounded-lg p-2 border border-gray-700 mb-2">
        <div className="flex items-center justify-between mb-2">
          <h2 className={cn("text-lg font-semibold", color)}>{title}</h2>
          <div className="flex items-center space-x-4">
            {type === 'postMarket' && (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-green-600 text-green-400 hover:bg-green-600 hover:text-white",
                    !selectedPostMarketStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedPostMarketStrategy}
                  onClick={() => handlePostMarketStrategyAction('啟動')}
                >
                  <Play className="h-3 w-3 mr-1" />
                  啟動
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-red-600 text-red-400 hover:bg-red-600 hover:text-white",
                    !selectedPostMarketStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedPostMarketStrategy}
                  onClick={() => handlePostMarketStrategyAction('停止')}
                >
                  <Square className="h-3 w-3 mr-1" />
                  停止
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-blue-600 text-blue-400 hover:bg-blue-600 hover:text-white",
                    !selectedPostMarketStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedPostMarketStrategy}
                  onClick={() => handlePostMarketStrategyAction('回測')}
                >
                  <BarChart3 className="h-3 w-3 mr-1" />
                  回測
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-purple-600 text-purple-400 hover:bg-purple-600 hover:text-white",
                    !selectedPostMarketStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedPostMarketStrategy}
                  onClick={() => handlePostMarketStrategyAction('每日自動執行')}
                >
                  <Clock className="h-3 w-3 mr-1" />
                  每日自動執行
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-orange-600 text-orange-400 hover:bg-orange-600 hover:text-white",
                    !selectedPostMarketStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedPostMarketStrategy}
                  onClick={() => handlePostMarketStrategyAction('解除自動執行')}
                >
                  <XCircle className="h-3 w-3 mr-1" />
                  解除自動執行
                </Button>
              </div>
            )}
            {type === 'alert' && (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-green-600 text-green-400 hover:bg-green-600 hover:text-white",
                    !selectedAlertStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedAlertStrategy}
                  onClick={() => handleAlertStrategyAction('啟動')}
                >
                  <Play className="h-3 w-3 mr-1" />
                  啟動
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-red-600 text-red-400 hover:bg-red-600 hover:text-white",
                    !selectedAlertStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedAlertStrategy}
                  onClick={() => handleAlertStrategyAction('停止')}
                >
                  <Square className="h-3 w-3 mr-1" />
                  停止
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-blue-600 text-blue-400 hover:bg-blue-600 hover:text-white",
                    !selectedAlertStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedAlertStrategy}
                  onClick={() => handleAlertStrategyAction('回測')}
                >
                  <BarChart3 className="h-3 w-3 mr-1" />
                  回測
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-purple-600 text-purple-400 hover:bg-purple-600 hover:text-white",
                    !selectedAlertStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedAlertStrategy}
                  onClick={() => handleAlertStrategyAction('每日自動執行')}
                >
                  <Clock className="h-3 w-3 mr-1" />
                  每日自動執行
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-8 px-3 border-orange-600 text-orange-400 hover:bg-orange-600 hover:text-white",
                    !selectedAlertStrategy && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!selectedAlertStrategy}
                  onClick={() => handleAlertStrategyAction('解除自動執行')}
                >
                  <XCircle className="h-3 w-3 mr-1" />
                  解除自動執行
                </Button>
              </div>
            )}
            <Button
              variant="outline"
              size="sm"
              className="h-8 px-3 border-gray-600 text-gray-400 hover:bg-gray-600 hover:text-white"
              onClick={() => addStrategy(type)}
            >
              <span className="text-sm">+策略</span>
            </Button>
          </div>
        </div>

        <div className="rounded-md border border-gray-700">
          <Table>
            <TableHeader>
              <TableRow className="border-gray-600 hover:bg-gray-700/50">
                <TableHead className="text-gray-300">項數</TableHead>
                <TableHead className="text-gray-300">自動執行</TableHead>
                <TableHead className="text-gray-300">策略編號</TableHead>
                <TableHead className="text-gray-300">策略名稱</TableHead>
                <TableHead className="text-gray-300">交易商品</TableHead>
                <TableHead className="text-gray-300">多空方向</TableHead>
                <TableHead className="text-gray-300">指定頻率</TableHead>
                <TableHead className="text-gray-300">啟動時間</TableHead>
                <TableHead className="text-gray-300">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {strategies.map((strategy, index) => (
                <TableRow
                  key={strategy.id}
                  className={cn(
                    "border-gray-700 hover:bg-gray-700/30",
                    (type === 'postMarket' || type === 'alert') && "cursor-pointer",
                    type === 'postMarket' && selectedPostMarketStrategy === strategy.id && "bg-blue-600/20 border-blue-500",
                    type === 'alert' && selectedAlertStrategy === strategy.id && "bg-yellow-600/20 border-yellow-500"
                  )}
                  onClick={() => {
                    if (type === 'postMarket') handlePostMarketStrategyClick(strategy.id);
                    if (type === 'alert') handleAlertStrategyClick(strategy.id);
                  }}
                >
                  <TableCell className="text-white">{index + 1}</TableCell>
                  <TableCell>
                    <span className={cn(
                      "px-2 py-1 rounded text-xs font-medium",
                      strategy.autoExecute ? "bg-green-600 text-white" : "bg-gray-600 text-gray-300"
                    )}>
                      {strategy.autoExecute ? "已啟用" : "未啟用"}
                    </span>
                  </TableCell>
                  <TableCell className={cn(
                    "font-mono",
                    type === 'postMarket' ? 'text-orange-400' :
                    type === 'alert' ? 'text-yellow-400' : 'text-green-400'
                  )}>
                    {strategy.strategyCode}
                  </TableCell>
                  <TableCell className="text-white">{strategy.strategyName}</TableCell>
                  <TableCell>
                    <span className="px-2 py-1 bg-gray-600 rounded text-xs text-white">
                      {strategy.tradingProduct}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className={cn(
                      "px-2 py-1 rounded text-xs text-white",
                      strategy.direction === '多頭' ? 'bg-red-600' :
                      strategy.direction === '空頭' ? 'bg-green-600' : 'bg-blue-600'
                    )}>
                      {strategy.direction}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className={cn(
                      "px-2 py-1 rounded text-xs text-white",
                      strategy.frequency === '即時' ? 'bg-orange-600' :
                      strategy.frequency === '每日' ? 'bg-blue-600' : 'bg-indigo-600'
                    )}>
                      {strategy.frequency}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className={cn(
                      "font-mono text-xs",
                      strategy.enabled ? "text-green-400" : "text-gray-500"
                    )}>
                      {strategy.enabled ? strategy.activationTime : "未啟動"}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 border-gray-600 hover:bg-gray-700"
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 border-gray-600 hover:bg-red-700 hover:border-red-600"
                        onClick={() => deleteStrategy(type, strategy.id)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  };

  // 獲取所有啟用的策略用於篩選結果顯示
  const getFilteredStrategies = () => {
    const allStrategies = [
      ...postMarketStrategies.filter(s => s.enabled),
      ...alertStrategies.filter(s => s.enabled),
      ...tradingStrategies.filter(s => s.enabled)
    ];
    return allStrategies;
  };

  // 處理盤後選股策略操作
  const handlePostMarketStrategyAction = (action: string, strategyId?: number) => {
    if (!selectedPostMarketStrategy && !strategyId) return;

    const targetStrategyId = strategyId || selectedPostMarketStrategy;
    const strategy = postMarketStrategies.find(s => s.id === targetStrategyId);

    if (!strategy) return;

    switch (action) {
      case '啟動':
        // 啟動策略並顯示結果
        setPostMarketStrategies(prev =>
          prev.map(s => s.id === targetStrategyId ? { ...s, enabled: true } : s)
        );
        setShowStrategyResults(true);
        setCurrentStrategyName(strategy.strategyName);
        break;

      case '停止':
        // 停止策略
        setPostMarketStrategies(prev =>
          prev.map(s => s.id === targetStrategyId ? { ...s, enabled: false } : s)
        );
        if (targetStrategyId === selectedPostMarketStrategy) {
          setShowStrategyResults(false);
          setCurrentStrategyName('');
        }
        break;

      case '回測':
        // 回測不改變啟用狀態，但顯示結果
        setShowStrategyResults(true);
        setCurrentStrategyName(`${strategy.strategyName} (回測)`);
        break;

      case '每日自動執行':
        // 設置每日自動執行並顯示結果
        setPostMarketStrategies(prev =>
          prev.map(s => s.id === targetStrategyId ? { ...s, enabled: true, autoExecute: true } : s)
        );
        setShowStrategyResults(true);
        setCurrentStrategyName(`${strategy.strategyName} (每日自動)`);
        break;

      case '解除自動執行':
        // 解除自動執行
        setPostMarketStrategies(prev =>
          prev.map(s => s.id === targetStrategyId ? { ...s, enabled: false, autoExecute: false } : s)
        );
        if (targetStrategyId === selectedPostMarketStrategy) {
          setShowStrategyResults(false);
          setCurrentStrategyName('');
        }
        break;
    }
  };

  // 點選盤後選股策略項目
  const handlePostMarketStrategyClick = (strategyId: number) => {
    setSelectedPostMarketStrategy(strategyId);
  };

  // 處理盤中警示策略操作
  const handleAlertStrategyAction = (action: string, strategyId?: number) => {
    if (!selectedAlertStrategy && !strategyId) return;

    const targetStrategyId = strategyId || selectedAlertStrategy;
    const strategy = alertStrategies.find(s => s.id === targetStrategyId);

    if (!strategy) return;

    switch (action) {
      case '啟動':
        // 啟動策略並顯示結果
        setAlertStrategies(prev =>
          prev.map(s => s.id === targetStrategyId ? { ...s, enabled: true } : s)
        );
        setShowStrategyResults(true);
        setCurrentStrategyName(strategy.strategyName);
        break;
      case '停止':
        // 停止策略
        setAlertStrategies(prev =>
          prev.map(s => s.id === targetStrategyId ? { ...s, enabled: false } : s)
        );
        if (targetStrategyId === selectedAlertStrategy) {
          setShowStrategyResults(false);
          setCurrentStrategyName('');
        }
        break;
      case '回測':
        // 執行回測
        setShowStrategyResults(true);
        setCurrentStrategyName(`${strategy.strategyName} - 回測結果`);
        break;
      case '每日自動執行':
        // 設定每日自動執行
        setAlertStrategies(prev =>
          prev.map(s => s.id === targetStrategyId ? { ...s, enabled: true, autoExecute: true } : s)
        );
        setShowStrategyResults(true);
        setCurrentStrategyName(`${strategy.strategyName} - 每日自動執行`);
        break;
      case '解除自動執行':
        // 解除自動執行
        setAlertStrategies(prev =>
          prev.map(s => s.id === targetStrategyId ? { ...s, enabled: false, autoExecute: false } : s)
        );
        if (targetStrategyId === selectedAlertStrategy) {
          setShowStrategyResults(false);
          setCurrentStrategyName('');
        }
        break;
    }
  };

  // 點選盤中警示策略項目
  const handleAlertStrategyClick = (strategyId: number) => {
    setSelectedAlertStrategy(strategyId);
  };

  // 獲取篩選後的自選股數據
  const getFilteredStockPool = () => {
    if (selectedStockPoolType === '全部') {
      return stockPool;
    }
    return stockPool.filter(stock => stock.type === selectedStockPoolType);
  };

  // 處理自選股股池中的股票選擇
  const handlePoolStockSelection = (stockCode: string) => {
    setSelectedPoolStocks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stockCode)) {
        newSet.delete(stockCode);
      } else {
        newSet.add(stockCode);
      }
      return newSet;
    });
  };

  // 全選自選股股池中的股票
  const handleSelectAllPoolStocks = () => {
    const filteredStocks = getFilteredStockPool();
    const allStockCodes = filteredStocks.map(stock => stock.code);

    // 檢查是否已經全選
    const isAllSelected = allStockCodes.every(code => selectedPoolStocks.has(code));

    if (isAllSelected) {
      // 如果已經全選，則取消全選
      setSelectedPoolStocks(prev => {
        const newSet = new Set(prev);
        allStockCodes.forEach(code => newSet.delete(code));
        return newSet;
      });
    } else {
      // 如果未全選，則全選當前篩選的股票
      setSelectedPoolStocks(prev => {
        const newSet = new Set(prev);
        allStockCodes.forEach(code => newSet.add(code));
        return newSet;
      });
    }
  };

  // 移除選中的股票
  const handleRemoveSelectedStocks = () => {
    if (selectedPoolStocks.size === 0) {
      alert('請先選擇要移除的股票');
      return;
    }

    const removedCount = selectedPoolStocks.size;

    // 從股票池中移除選中的股票
    setStockPool(prev => prev.filter(stock => !selectedPoolStocks.has(stock.code)));

    // 清除選擇狀態
    setSelectedPoolStocks(new Set());

    // 顯示成功訊息
    alert(`已移除 ${removedCount} 支股票`);
  };

  // 處理導航頁面切換
  const handleNavigation = (page: string) => {
    setCurrentNavPage(page);

    switch (page) {
      case '策略管理':
        setShowLiveMonitorSubmenu(false);
        setCurrentPageContent('策略設定');
        setSelectedStrategyCategory('strategySettings');
        break;
      case '基本設定1':
        setShowLiveMonitorSubmenu(false);
        setCurrentPageContent('基本設定');
        break;
    }
  };

  // 處理實盤監測子選單點擊
  const handleLiveMonitorSubmenu = (submenu: string) => {
    setShowLiveMonitorSubmenu(false);
    setCurrentNavPage('策略管理');

    switch (submenu) {
      case '策略設定':
        setCurrentPageContent('策略設定');
        break;
      case '執行狀態':
        setCurrentPageContent('執行狀態');
        break;
      default:
        setCurrentPageContent('實盤監測');
        break;
    }
  };

  // 處理策略側邊選單點擊
  const handleStrategyCategorySelect = (category: string) => {
    setSelectedStrategyCategory(category);
  };

  // 處理股票選擇
  const handleStockSelection = (stockIndex: number) => {
    setSelectedStocks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stockIndex)) {
        newSet.delete(stockIndex);
      } else {
        newSet.add(stockIndex);
      }
      return newSet;
    });
  };

  // 全選股票
  const handleSelectAllStocks = () => {
    const allStockIndexes = stockData.map(stock => stock.index);
    setSelectedStocks(new Set(allStockIndexes));
  };

  // 清除選擇
  const handleClearSelection = () => {
    setSelectedStocks(new Set());
  };

  // 加入自選股股池
  const handleAddToStockPool = () => {
    if (selectedStocks.size === 0) {
      alert('請先選擇要加入的股票');
      return;
    }
    setShowAddToPoolModal(true);
  };

  // 確認加入自選股股池
  const handleConfirmAddToPool = (selectedType: string) => {
    // 獲取選中的股票數據
    const selectedStockData = stockData.filter(stock => selectedStocks.has(stock.index));

    // 轉換為自選股股池格式並加入
    const newStocks = selectedStockData.map(stock => ({
      code: stock.code,
      name: stock.name,
      type: selectedType,
      price: parseFloat(stock.price.toString()),
      change: parseFloat(stock.change.toString()),
      volume: stock.volume,
      addedDate: new Date().toLocaleDateString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).replace(/\//g, '/'),
      note: '策略篩選加入'
    }));

    // 檢查重複並過濾
    const existingCodes = new Set(stockPool.map(stock => stock.code));
    const uniqueNewStocks = newStocks.filter(stock => !existingCodes.has(stock.code));

    if (uniqueNewStocks.length === 0) {
      alert('選中的股票已存在於自選股股池中');
      setSelectedStocks(new Set());
      setShowAddToPoolModal(false);
      return;
    }

    // 加入自選股股池
    setStockPool(prev => [...prev, ...uniqueNewStocks]);

    // 清除選擇並關閉彈跳視窗
    setSelectedStocks(new Set());
    setShowAddToPoolModal(false);

    // 顯示成功訊息
    alert(`已將 ${uniqueNewStocks.length} 支股票加入 ${selectedType} 類型的自選股股池`);
  };

  // 日期處理函數
  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    const weekday = weekdays[date.getDay()];

    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();

    return `${year}年 ${month}月 ${day}日 (${isToday ? '最新' : `週${weekday}`})`;
  };

  // 日期調整函數
  const adjustDate = (days: number) => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + days);
    setSelectedDate(newDate);
  };

  // 日期選擇函數
  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    setShowDatePicker(false);
  };

  // 欄位管理函數
  const toggleColumn = (columnId: string) => {
    setColumns(prev =>
      prev.map(col =>
        col.id === columnId ? { ...col, enabled: !col.enabled } : col
      )
    );
  };

  const moveColumn = (columnId: string, direction: 'up' | 'down') => {
    setColumns(prev => {
      const currentIndex = prev.findIndex(col => col.id === columnId);
      if (currentIndex === -1) return prev;

      const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
      if (newIndex < 0 || newIndex >= prev.length) return prev;

      const newColumns = [...prev];
      [newColumns[currentIndex], newColumns[newIndex]] = [newColumns[newIndex], newColumns[currentIndex]];
      return newColumns;
    });
  };

  const resetColumns = () => {
    setColumns(availableColumns);
  };

  // 獲取欄位值的函數
  const getCellValue = (stock: any, columnId: string) => {
    switch (columnId) {
      case 'select':
        return (
          <input
            type="checkbox"
            className="w-3 h-3"
            checked={selectedStocks.has(stock.index)}
            onChange={() => handleStockSelection(stock.index)}
          />
        );
      case 'index':
        return <span>{stock.index}</span>;
      case 'code':
        return <span className="text-orange-400">{stock.code}</span>;
      case 'name':
        return stock.name;
      case 'price':
        return <span className="text-red-400">{stock.price}</span>;
      case 'change':
        return (
          <span className={stock.change >= 0 ? "text-red-400" : "text-green-400"}>
            {stock.change >= 0 ? stock.change : stock.change}
          </span>
        );
      case 'volume':
        return stock.volume;
      case 'monthlyGrowth':
        return <span className="text-red-400">{stock.monthlyGrowth}</span>;
      case 'yearlyGrowth':
        return <span className="text-red-400">{stock.yearlyGrowth}</span>;
      case 'marketCap':
        return stock.marketCap;
      case 'pe':
        return stock.pe;
      case 'pb':
        return stock.pb;
      case 'dividend':
        return stock.dividend;
      case 'eps':
        return stock.eps;
      case 'roe':
        return stock.roe;
      default:
        return '';
    }
  };

  // 面板大小變化處理函數
  const handlePanelResize = (sizes: number[]) => {
    const newLeftPanelSize = sizes[0];
    setLeftPanelSize(newLeftPanelSize);

    // 保存到 localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('strategy-panel-size', newLeftPanelSize.toString());
    }
  };

  return (
    <div className="h-screen flex flex-col">
      {/* 固定頂部標題欄 */}
      <header className="flex-shrink-0 bg-green-500 border-b border-green-600 shadow-lg">
        {/* 第一列：系統標題和版本資訊 */}
        <div className="px-4 py-2 bg-green-400">
          <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-lg font-bold text-white">CHIPO Auto Trading(個人版)</h1>
            <span className="text-sm text-gray-400">[版本 3.12.05 240229]</span>
            <span className="text-sm text-green-400">[P120416374:已登入]</span>
          </div>
          <div className="text-sm text-gray-400">
            {new Date().toLocaleDateString('zh-TW', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              weekday: 'short'
            })}
          </div>
        </div>
        </div>

        {/* 第二列：主要導航選項 */}
        <div className="px-4 py-2 bg-green-600 relative">
        <nav className="flex items-center space-x-6">
          {['策略管理', '基本設定1'].map((page) => (
            <div key={page} className="relative">
              <button
                onClick={() => handleNavigation(page)}
                className={cn(
                  "px-3 py-1 rounded transition-colors flex items-center",
                  currentNavPage === page
                    ? "bg-blue-600 text-white"
                    : "text-white hover:bg-gray-700"
                )}
              >
                {page}
              </button>
            </div>
          ))}
        </nav>
        </div>
      </header>

      {/* 固定分割線 */}
      <div className="h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 shadow-lg"></div>

      {/* 主要內容區域 */}
      <div className="flex-1 bg-gray-100 overflow-hidden">
      {/* 根據選擇的頁面顯示不同內容 */}
      {currentPageContent === '策略設定' ? (
        <div className="flex h-full">
          {/* 策略側邊選單 */}
          <StrategySidebar
            onCategorySelect={handleStrategyCategorySelect}
            selectedCategory={selectedStrategyCategory}
          />

          {/* 主要策略設定區域 */}
          <PanelGroup direction="horizontal" className="flex-1" onLayout={handlePanelResize}>
            {/* 左側策略設定區域 */}
            <Panel defaultSize={leftPanelSize} minSize={50} maxSize={80}>
              <div className="h-full p-2 overflow-auto bg-gray-800">
            {selectedStrategyCategory === 'strategySettings' && (
              <div className="space-y-2">
                <StrategyTable
                  title="盤後選股策略"
                  strategies={postMarketStrategies}
                  type="postMarket"
                  color="text-orange-400"
                  tableRef={postMarketRef}
                />

                <StrategyTable
                  title="盤中警示策略"
                  strategies={alertStrategies}
                  type="alert"
                  color="text-yellow-400"
                  tableRef={alertRef}
                />

                <StrategyTable
                  title="盤中交易策略"
                  strategies={tradingStrategies}
                  type="trading"
                  color="text-green-400"
                  tableRef={tradingRef}
                />
              </div>
            )}

            {selectedStrategyCategory === 'executionStatus' && (
              <div className="p-4 text-center">
                <h2 className="text-xl font-bold text-white mb-4">執行狀態</h2>
                <p className="text-gray-400">策略執行狀態監控功能開發中...</p>
              </div>
            )}
          </div>
        </Panel>

        <PanelResizeHandle className="w-1 bg-gray-600 hover:bg-gray-500 transition-colors" />

        {/* 右側策略篩選結果區域 */}
        <Panel defaultSize={100 - leftPanelSize} minSize={20} maxSize={50}>
          <PanelGroup direction="vertical" className="h-full">
            {/* 上方：策略篩選結果 */}
            <Panel defaultSize={60} minSize={30} maxSize={80}>
              <div ref={strategyResultsRef} className="h-full bg-gray-800 border-l border-gray-700 px-2 pt-2 pb-2 overflow-auto relative">
            <div className="mb-2">
              <h2 className="text-xl font-bold text-white mb-1">
                {showStrategyResults && currentStrategyName ?
                  `策略篩選結果 - ${currentStrategyName}` :
                  '策略篩選結果'
                }
              </h2>
            </div>

            {/* 操作按鈕列 */}
            <div className="flex items-center space-x-2 mb-1 text-xs">
              <button
                onClick={() => handleAddToStockPool()}
                className="flex items-center space-x-1 px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700"
              >
                <span>+</span>
                <span>加自選</span>
              </button>
              <button
                onClick={() => handleSelectAllStocks()}
                className="flex items-center space-x-1 px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                <span>✓</span>
                <span>全選</span>
              </button>
              <button
                onClick={() => handleClearSelection()}
                className="flex items-center space-x-1 px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700"
              >
                <span>✗</span>
                <span>取消</span>
              </button>
              <button className="flex items-center space-x-1 px-2 py-1 bg-gray-600 text-white rounded hover:bg-gray-700">
                <span>📤</span>
                <span>匯出</span>
              </button>
              <button className="flex items-center space-x-1 px-2 py-1 bg-purple-600 text-white rounded hover:bg-purple-700">
                <span>🔄</span>
                <span>執行</span>
              </button>
              <button className="flex items-center space-x-1 px-2 py-1 bg-gray-600 text-white rounded hover:bg-gray-700">
                <span>⏸</span>
                <span>停止</span>
              </button>
              <button className="flex items-center space-x-1 px-2 py-1 bg-orange-600 text-white rounded hover:bg-orange-700">
                <span>📊</span>
                <span>回測</span>
              </button>
              <button
                className="flex items-center space-x-1 px-2 py-1 bg-indigo-600 text-white rounded hover:bg-indigo-700"
                onClick={() => setShowColumnSettings(true)}
              >
                <span>📋</span>
                <span>欄位</span>
              </button>
            </div>

            {/* 日期和搜尋列 */}
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2 text-xs">
                <span className="text-gray-400">最新</span>
                <button
                  className="px-1 py-1 bg-gray-700 text-white rounded hover:bg-gray-600"
                  onClick={() => adjustDate(-1)}
                >
                  ◀
                </button>
                <button
                  className="px-1 py-1 bg-gray-700 text-white rounded hover:bg-gray-600"
                  onClick={() => adjustDate(1)}
                >
                  ▶
                </button>
                <div className="relative" ref={datePickerRef}>
                  <button
                    className="text-blue-400 hover:text-blue-300 cursor-pointer"
                    onClick={() => setShowDatePicker(!showDatePicker)}
                  >
                    📅 {formatDate(selectedDate)}
                  </button>
                  {showDatePicker && (
                    <div className="absolute top-full left-0 mt-1 bg-gray-800 border border-gray-600 rounded-lg p-3 z-50 shadow-lg">
                      <input
                        type="date"
                        value={selectedDate.toISOString().split('T')[0]}
                        onChange={(e) => handleDateSelect(new Date(e.target.value))}
                        className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-xs"
                      />
                    </div>
                  )}
                </div>
                <span className="text-gray-400">符合筆數(101)</span>
              </div>
              <div className="flex items-center">
                <input
                  type="text"
                  placeholder="搜尋"
                  className="px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-xs w-20"
                />
                <button className="ml-1 px-2 py-1 bg-gray-600 text-white rounded text-xs">🔍</button>
              </div>
            </div>

            {/* 股票表格 */}
            <div className="overflow-auto">
              <table className="w-full text-xs">
                <thead className="bg-gray-700 sticky top-0">
                  <tr className="text-gray-300">
                    {columns.filter(col => col.enabled).map((col, index, enabledCols) => (
                      <th
                        key={col.id}
                        className={cn(
                          "px-1 py-1",
                          col.id === 'select' || col.id === 'index' || col.id === 'code' || col.id === 'name' ? "text-left" : "text-right",
                          index < enabledCols.length - 1 ? "border-r border-gray-600" : ""
                        )}
                      >
                        {col.name}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="text-white">
                  {stockData.map((stock) => (
                    <tr key={stock.code} className="hover:bg-gray-700 border-b border-gray-800">
                      {columns.filter(col => col.enabled).map((col, index, enabledCols) => (
                        <td
                          key={col.id}
                          className={cn(
                            "px-1 py-1",
                            col.id === 'select' || col.id === 'index' || col.id === 'code' || col.id === 'name' ? "text-left" : "text-right",
                            index < enabledCols.length - 1 ? "border-r border-gray-700" : ""
                          )}
                        >
                          {getCellValue(stock, col.id)}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 欄位設定彈跳視窗 - 限定在策略篩選結果區域內 */}
            {showColumnSettings && (
              <div
                className="absolute inset-0 z-[9999] pointer-events-auto"
                onClick={() => setShowColumnSettings(false)}
              >
                <div
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white border border-gray-400 rounded shadow-lg w-80"
                  onClick={(e) => e.stopPropagation()}
                >
                  {/* 標題欄 */}
                  <div className="bg-blue-100 border-b border-gray-300 px-3 py-2 flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-800">調整欄位</h3>
                    <button
                      onClick={() => setShowColumnSettings(false)}
                      className="text-gray-500 hover:text-gray-700 text-lg leading-none"
                    >
                      ×
                    </button>
                  </div>

                  {/* 欄位列表 */}
                  <div className="p-3 max-h-60 overflow-y-auto">
                    {columns.map((column, index) => (
                      <div
                        key={column.id}
                        className="flex items-center justify-between py-1 hover:bg-gray-50"
                      >
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={column.enabled}
                            onChange={() => !column.fixed && toggleColumn(column.id)}
                            disabled={column.fixed}
                            className="w-4 h-4"
                          />
                          <span className={cn(
                            "text-sm text-gray-800",
                            column.fixed && "font-medium"
                          )}>
                            {column.name}
                          </span>
                        </div>
                        <div className="flex flex-col items-center">
                          <button
                            onClick={() => moveColumn(column.id, 'up')}
                            disabled={index === 0}
                            className="p-0 text-gray-500 hover:text-gray-700 disabled:opacity-30 disabled:cursor-not-allowed text-xs leading-none"
                          >
                            ▲
                          </button>
                          <button
                            onClick={() => moveColumn(column.id, 'down')}
                            disabled={index === columns.length - 1}
                            className="p-0 text-gray-500 hover:text-gray-700 disabled:opacity-30 disabled:cursor-not-allowed text-xs leading-none"
                          >
                            ▼
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* 底部按鈕 */}
                  <div className="border-t border-gray-300 px-3 py-2 flex items-center justify-end space-x-2 bg-gray-50">
                    <button
                      onClick={resetColumns}
                      className="px-3 py-1 text-xs border border-gray-300 bg-white text-gray-700 rounded hover:bg-gray-100"
                    >
                      重設預設
                    </button>
                    <button
                      onClick={() => setShowColumnSettings(false)}
                      className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                      確定
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* 加入自選股類型選擇彈跳視窗 */}
            {showAddToPoolModal && (
              <div
                className="absolute inset-0 z-[9999] pointer-events-auto"
                onClick={() => setShowAddToPoolModal(false)}
              >
                <div
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white border border-gray-400 rounded shadow-lg w-80"
                  onClick={(e) => e.stopPropagation()}
                >
                  {/* 標題欄 */}
                  <div className="bg-blue-100 border-b border-gray-300 px-3 py-2 flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-800">選擇股票類型</h3>
                    <button
                      onClick={() => setShowAddToPoolModal(false)}
                      className="text-gray-500 hover:text-gray-700 text-lg leading-none"
                    >
                      ×
                    </button>
                  </div>

                  {/* 內容區域 */}
                  <div className="p-4">
                    <p className="text-sm text-gray-700 mb-3">
                      已選擇 {selectedStocks.size} 支股票，請選擇要加入的類型：
                    </p>

                    <div className="space-y-2">
                      {['日內當沖', '日波段', '週波段', '月波段'].map((type) => (
                        <button
                          key={type}
                          onClick={() => handleConfirmAddToPool(type)}
                          className={cn(
                            "w-full px-3 py-2 text-left rounded border-2 transition-colors",
                            type === '日內當沖' ? "border-red-500 bg-red-50 hover:bg-red-100 text-red-700"
                            : type === '日波段' ? "border-blue-500 bg-blue-50 hover:bg-blue-100 text-blue-700"
                            : type === '週波段' ? "border-green-500 bg-green-50 hover:bg-green-100 text-green-700"
                            : "border-purple-500 bg-purple-50 hover:bg-purple-100 text-purple-700"
                          )}
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{type}</span>
                            <span className="text-xs opacity-70">
                              {type === '日內當沖' ? '當日買賣'
                              : type === '日波段' ? '1-3天持有'
                              : type === '週波段' ? '1-2週持有'
                              : '1個月以上持有'}
                            </span>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* 底部按鈕 */}
                  <div className="border-t border-gray-300 px-3 py-2 flex items-center justify-end">
                    <button
                      onClick={() => setShowAddToPoolModal(false)}
                      className="px-3 py-1 text-xs border border-gray-300 bg-white text-gray-700 rounded hover:bg-gray-100"
                    >
                      取消
                    </button>
                  </div>
                </div>
              </div>
            )}
              </div>
            </Panel>

            <PanelResizeHandle className="h-1 bg-gray-600 hover:bg-gray-500 transition-colors" />

            {/* 下方：自選股股池 */}
            <Panel defaultSize={40} minSize={20} maxSize={70}>
              <div className="h-full bg-gray-800 border-l border-gray-700 px-2 pt-2 pb-2 overflow-auto">
                {/* 標題列與分類標籤 */}
                <div className="flex items-center justify-between mb-2">
                  <h2 className="text-xl font-bold text-white">自選股股池</h2>
                  <div className="flex items-center space-x-1 text-xs">
                    {['日內當沖', '日波段', '週波段', '月波段', '全部'].map((type) => (
                      <button
                        key={type}
                        onClick={() => setSelectedStockPoolType(type)}
                        className={cn(
                          "px-2 py-1 rounded border-2 transition-colors",
                          selectedStockPoolType === type
                            ? type === '日內當沖' ? "bg-red-600 text-white border-red-500"
                            : type === '日波段' ? "bg-blue-600 text-white border-blue-500"
                            : type === '週波段' ? "bg-green-600 text-white border-green-500"
                            : type === '月波段' ? "bg-purple-600 text-white border-purple-500"
                            : "bg-orange-600 text-white border-orange-500"
                            : "bg-gray-600 text-gray-300 border-transparent hover:bg-gray-500"
                        )}
                      >
                        {type}
                      </button>
                    ))}
                  </div>
                </div>

                {/* 自選股操作按鈕列 */}
                <div className="flex items-center space-x-2 mb-1 text-xs">
                  <button
                    onClick={() => handleSelectAllPoolStocks()}
                    className="flex items-center space-x-1 px-2 py-1 bg-gray-600 text-white rounded hover:bg-gray-700"
                  >
                    <span>✓</span>
                    <span>全選</span>
                  </button>
                  <button className="flex items-center space-x-1 px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                    <span>+</span>
                    <span>新增股票</span>
                  </button>
                  <button
                    onClick={() => handleRemoveSelectedStocks()}
                    className="flex items-center space-x-1 px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    <span>-</span>
                    <span>移除選中</span>
                  </button>
                  <button className="flex items-center space-x-1 px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                    <span>📊</span>
                    <span>分析</span>
                  </button>
                  <button className="flex items-center space-x-1 px-2 py-1 bg-purple-600 text-white rounded hover:bg-purple-700">
                    <span>📁</span>
                    <span>匯出</span>
                  </button>
                </div>

                {/* 自選股表格 */}
                <div className="bg-gray-900 rounded border border-gray-700 overflow-hidden">
                  <table className="w-full text-xs">
                    <thead className="bg-gray-700">
                      <tr>
                        <th className="text-left p-2 text-gray-300">選擇</th>
                        <th className="text-left p-2 text-gray-300">代碼</th>
                        <th className="text-left p-2 text-gray-300">名稱</th>
                        <th className="text-left p-2 text-gray-300">類型</th>
                        <th className="text-left p-2 text-gray-300">現價</th>
                        <th className="text-left p-2 text-gray-300">漲跌%</th>
                        <th className="text-left p-2 text-gray-300">成交量</th>
                        <th className="text-left p-2 text-gray-300">加入時間</th>
                        <th className="text-left p-2 text-gray-300">備註</th>
                      </tr>
                    </thead>
                    <tbody>
                      {/* 動態自選股數據 - 根據選擇的類型篩選 */}
                      {getFilteredStockPool().map((stock, index) => (
                        <tr key={stock.code} className="border-b border-gray-700 hover:bg-gray-700/30">
                          <td className="p-2">
                            <input
                              type="checkbox"
                              className="w-3 h-3"
                              checked={selectedPoolStocks.has(stock.code)}
                              onChange={() => handlePoolStockSelection(stock.code)}
                            />
                          </td>
                          <td className="p-2 text-orange-400 font-mono">{stock.code}</td>
                          <td className="p-2 text-white">{stock.name}</td>
                          <td className="p-2">
                            <span className={cn(
                              "px-1 py-0.5 text-white rounded text-xs scale-75 inline-block transform origin-left",
                              stock.type === '日內當沖' ? "bg-red-600"
                              : stock.type === '日波段' ? "bg-blue-600"
                              : stock.type === '週波段' ? "bg-green-600"
                              : "bg-purple-600"
                            )}>
                              {stock.type}
                            </span>
                          </td>
                          <td className={cn(
                            "p-2",
                            stock.change >= 0 ? "text-red-400" : "text-green-400"
                          )}>
                            {stock.price}
                          </td>
                          <td className={cn(
                            "p-2",
                            stock.change >= 0 ? "text-red-400" : "text-green-400"
                          )}>
                            {stock.change >= 0 ? '+' : ''}{stock.change.toFixed(2)}%
                          </td>
                          <td className="p-2 text-gray-300">{stock.volume}</td>
                          <td className="p-2 text-gray-400">{stock.addedDate}</td>
                          <td className="p-2 text-gray-400">{stock.note}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </Panel>
          </PanelGroup>
        </Panel>
      </PanelGroup>
        </div>
      ) : (
        /* 其他頁面的佔位內容 */
        <div className="h-full flex items-center justify-center">
          <div className="text-center">
            <div className="text-6xl mb-4">
              {currentPageContent === '實盤監測' && '📊'}
              {currentPageContent === '基本設定' && '🔧'}
            </div>
            <h2 className="text-2xl font-bold text-white mb-4">
              {currentPageContent}
            </h2>
            <p className="text-gray-400 mb-6">
              {currentPageContent === '實盤監測' && '實時監控交易狀況'}
              {currentPageContent === '基本設定' && '系統基本參數設定'}
            </p>
          </div>
        </div>
      )}
    </div>
    </div>
  );
};

export default StrategySettingsPage;
