import React, { useState, useRef, useCallback, useMemo } from 'react';
import StockList from './StockList';
import RightPanelContainer from './RightPanelContainer';
import TaiwanStockSidebar from './TaiwanStockSidebar';
import ListedStockCategories from './ListedStockCategories';
import OTCStockCategories from './OTCStockCategories';
import StockFutures from './StockFutures';
import { usePanelContext } from '../contexts/PanelContext';

const TaiwanStockPage = () => {
  const { panels, updatePanel, getPanelById, updateTaiwanCategoryPanel } = usePanelContext();
  
  // 初始面板大小 (百分比) - 調整為三欄布局
  const [panelSizes, setPanelSizes] = useState({
    topLeft: { width: 40, height: 60 },
    topRight: { width: 60, height: 100 }, // 右側整合面板佔據整個右側
    bottomLeft: { width: 40, height: 40 },
  });

  const [selectedCategory, setSelectedCategory] = useState('listed');
  const [selectedStockCategory, setSelectedStockCategory] = useState<{code: string, name: string} | undefined>(undefined);

  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<string | null>(null);

  const handleMouseDown = useCallback((divider: string) => {
    setIsDragging(divider);
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    setPanelSizes(prev => {
      const newSizes = { ...prev };
      
      if (isDragging === 'vertical') {
        // 垂直分割線 - 調整左側和右側寬度
        const newLeftWidth = Math.max(20, Math.min(80, x));
        const newRightWidth = 100 - newLeftWidth;
        
        newSizes.topLeft.width = newLeftWidth;
        newSizes.bottomLeft.width = newLeftWidth;
        newSizes.topRight.width = newRightWidth;

        // 同步更新面板配置
        updatePanel('tw-top-left', { size: { width: newLeftWidth, height: newSizes.topLeft.height } });
        updatePanel('tw-top-right', { size: { width: newRightWidth, height: newSizes.topRight.height } });
        updatePanel('tw-bottom-left', { size: { width: newLeftWidth, height: newSizes.bottomLeft.height } });
      } else if (isDragging === 'horizontal') {
        // 水平分割線 - 只調整左側上下高度，右側保持100%
        const newTopHeight = Math.max(20, Math.min(80, y));
        const newBottomHeight = 100 - newTopHeight;
        
        newSizes.topLeft.height = newTopHeight;
        newSizes.bottomLeft.height = newBottomHeight;

        // 同步更新面板配置
        updatePanel('tw-top-left', { size: { width: newSizes.topLeft.width, height: newTopHeight } });
        updatePanel('tw-bottom-left', { size: { width: newSizes.bottomLeft.width, height: newBottomHeight } });
      }
      
      return newSizes;
    });
  }, [isDragging, updatePanel]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(null);
  }, []);

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = isDragging === 'vertical' ? 'col-resize' : 'row-resize';
      document.body.style.userSelect = 'none';
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 處理類股選擇變更 - 更新左上角面板的索引碼
  const handleCategorySelect = useCallback((category: string) => {
    setSelectedCategory(category);
    // 當切換類股時，清除已選擇的個股類別
    setSelectedStockCategory(undefined);
    
    // 更新左上角面板的索引碼
    if (updateTaiwanCategoryPanel) {
      const categoryMap: Record<string, 'listed' | 'otc' | 'futures'> = {
        'listed': 'listed',
        'otc': 'otc',
        'futures': 'futures'
      };
      updateTaiwanCategoryPanel(categoryMap[category]);
    }
  }, [updateTaiwanCategoryPanel]);

  // 處理類股中個股的選擇
  const handleStockCategorySelect = useCallback((categoryCode: string, categoryName: string) => {
    setSelectedStockCategory({ code: categoryCode, name: categoryName });
  }, []);

  // 根據選擇的類股渲染左上角內容 - 使用 useMemo 優化，並改用 StockList 組件
  const renderTopLeftContent = useMemo(() => {
    switch (selectedCategory) {
      case 'listed':
        // 方案 A：使用 StockList 組件顯示上市19類，並傳遞點擊處理函數
        return <StockList 
          selectedCategory={{ code: 'listed', name: '上市19類' }} 
          onCategorySelect={handleStockCategorySelect}
        />;
      case 'otc':
        return <OTCStockCategories />; // 顯示上櫃類股
      case 'futures':
        return <StockFutures />; // 顯示股票期貨
      default:
        // 默認使用 StockList 組件
        return <StockList 
          selectedCategory={{ code: 'listed', name: '上市19類' }} 
          onCategorySelect={handleStockCategorySelect}
        />;
    }
  }, [selectedCategory, handleStockCategorySelect]);

  // 獲取面板配置
  const mainChartPanel = getPanelById('tw-top-left');
  const rightPanelConfig = getPanelById('tw-top-right');
  const categoryListPanel = getPanelById('tw-bottom-left');

  return (
    <div className="flex h-full">
      {/* 極簡左側選單 */}
      <TaiwanStockSidebar 
        onCategorySelect={handleCategorySelect}
        selectedCategory={selectedCategory}
      />
      
      {/* 主要交易區域 - 三欄布局 */}
      <div 
        ref={containerRef}
        className="flex-1 relative bg-gray-900 overflow-hidden"
        style={{ minHeight: '400px' }}
        data-container-id="taiwan-trading-area"
      >
        {/* 左上角面板 - A1-上市1 (使用 StockList 組件) */}
        <div 
          className="absolute bg-gray-900 border-r border-b border-gray-700"
          style={{
            left: 0,
            top: 0,
            width: `${panelSizes.topLeft.width}%`,
            height: `${panelSizes.topLeft.height}%`
          }}
          data-panel-id="tw-top-left"
          data-panel-title={mainChartPanel?.title}
          data-panel-component={mainChartPanel?.component}
        >

          <div className="h-full">
            {renderTopLeftContent}
          </div>
        </div>

        {/* 右側整合面板 - A1-上市右 (包含原 A1-上市3 和 A1-上市4) */}
        <RightPanelContainer
          className="absolute border-gray-700"
          style={{
            right: 0,
            top: 0,
            width: `${panelSizes.topRight.width}%`,
            height: `${panelSizes.topRight.height}%`
          }}
        />

        {/* 左下角面板 - A1-上市2 (台股主圖表/個股列表) */}
        <div 
          className="absolute bg-gray-900 border-r border-gray-700"
          style={{
            left: 0,
            bottom: 0,
            width: `${panelSizes.bottomLeft.width}%`,
            height: `${panelSizes.bottomLeft.height}%`
          }}
          data-panel-id="tw-bottom-left"
          data-panel-title={categoryListPanel?.title}
          data-panel-component={categoryListPanel?.component}
          data-current-category={selectedCategory}
          data-selected-stock-category={selectedStockCategory?.name}
        >

          <div className="h-full">
            <StockList selectedCategory={selectedStockCategory} />
          </div>
        </div>

        {/* 垂直分割線 - 分隔左側和右側 */}
        <div
          className="absolute top-0 bottom-0 w-1 bg-gray-600 hover:bg-blue-500 cursor-col-resize z-10 transition-colors"
          style={{
            left: `${panelSizes.topLeft.width}%`,
            transform: 'translateX(-50%)'
          }}
          onMouseDown={() => handleMouseDown('vertical')}
          data-divider-id="vertical-divider"
          data-divider-type="vertical"
        >
          <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-3 hover:bg-blue-500/20" />
        </div>

        {/* 水平分割線 - 只分隔左側上下 */}
        <div
          className="absolute left-0 h-1 bg-gray-600 hover:bg-blue-500 cursor-row-resize z-10 transition-colors"
          style={{
            top: `${panelSizes.topLeft.height}%`,
            width: `${panelSizes.topLeft.width}%`,
            transform: 'translateY(-50%)'
          }}
          onMouseDown={() => handleMouseDown('horizontal')}
          data-divider-id="horizontal-divider"
          data-divider-type="horizontal"
        >
          <div className="absolute inset-x-0 top-1/2 transform -translate-y-1/2 h-3 hover:bg-blue-500/20" />
        </div>

        {/* 拖拽時的覆蓋層 */}
        {isDragging && (
          <div className="absolute inset-0 z-20 bg-transparent" data-overlay-active="true" />
        )}
      </div>
    </div>
  );
};

export default TaiwanStockPage;