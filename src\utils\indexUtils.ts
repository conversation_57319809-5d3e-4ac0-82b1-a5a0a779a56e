import { IndexConfig, PanelIndexConfig, MAIN_CATEGORIES, MARKET_SUB_CATEGORIES, TAIWAN_STOCK_CATEGORIES, STRATEGY_SUB_CATEGORIES } from '../types/IndexTypes';

// 生成面板索引碼
export const generatePanelIndex = (
  mainCategory: keyof typeof MAIN_CATEGORIES,
  subCategory?: string,
  categoryType?: string,
  windowIndex?: number
): IndexConfig => {
  const main = MAIN_CATEGORIES[mainCategory];
  
  let index: IndexConfig = {
    mainCode: main.code,
    mainName: main.name
  };

  // 添加子項目編碼
  if (subCategory) {
    if (mainCategory === 'MARKET') {
      const sub = Object.values(MARKET_SUB_CATEGORIES).find(s => s.name === subCategory);
      if (sub) {
        index.subCode = sub.code;
        index.subName = sub.name;
      }
    } else if (mainCategory === 'STRATEGY') {
      const sub = Object.values(STRATEGY_SUB_CATEGORIES).find(s => s.name === subCategory);
      if (sub) {
        index.subCode = sub.code;
        index.subName = sub.name;
      }
    }
  }

  // 添加台股類股編碼
  if (categoryType && index.subCode === 'A1') {
    const category = Object.values(TAIWAN_STOCK_CATEGORIES).find(c => c.name === categoryType);
    if (category) {
      index.categoryCode = category.code;
      index.categoryName = category.name;
    }
  }

  // 添加視窗編碼
  if (windowIndex && index.subCode) {
    if (index.categoryCode) {
      // 台股有類股分類的情況：A1-上市1, A1-上櫃2, A1-期貨3
      index.windowCode = `${index.subCode}-${index.categoryCode}${windowIndex}`;
    } else {
      // 美股或其他情況：A2-1, A2-2
      index.windowCode = `${index.subCode}-${windowIndex}`;
    }
    index.windowIndex = windowIndex;
  }

  return index;
};

// 生成完整顯示編碼
export const generateDisplayCode = (index: IndexConfig): string => {
  if (index.windowCode) {
    return index.windowCode;
  } else if (index.subCode) {
    return index.subCode;
  } else {
    return index.mainCode;
  }
};

// 根據市場、類股和視窗位置生成面板索引
export const generatePanelIndexForMarket = (
  market: 'taiwan' | 'us',
  position: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight',
  categoryType?: string
): PanelIndexConfig => {
  const subCategory = market === 'taiwan' ? '台股' : '美股';
  
  // 修改視窗編號對應關係
  const windowIndexMap = {
    topLeft: 1,      // 左上角 = 1
    topRight: 3,     // 右上角 = 3
    bottomLeft: 2,   // 左下角 = 2
    bottomRight: 4   // 右下角 = 4
  };
  
  const windowIndex = windowIndexMap[position];
  
  // 對於台股，如果沒有指定類股類型，默認使用上市
  let finalCategoryType = categoryType;
  if (market === 'taiwan' && !categoryType) {
    finalCategoryType = '上市類股';
  }
  
  const index = generatePanelIndex('MARKET', subCategory, finalCategoryType, windowIndex);
  const displayCode = generateDisplayCode(index);
  
  return {
    id: `${market}-${position}${categoryType ? `-${categoryType}` : ''}`,
    index,
    displayCode
  };
};

// 根據台股類股類型生成面板索引
export const generateTaiwanStockPanelIndex = (
  categoryType: 'listed' | 'otc' | 'futures',
  position: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight'
): PanelIndexConfig => {
  const categoryMap = {
    listed: '上市類股',
    otc: '上櫃類股',
    futures: '股票期貨'
  };
  
  return generatePanelIndexForMarket('taiwan', position, categoryMap[categoryType]);
};

// 解析索引碼
export const parseIndexCode = (code: string): IndexConfig | null => {
  try {
    // 解析格式如 "A1-上市3" 或 "A2-2"
    const parts = code.split('-');
    
    if (parts.length === 1) {
      // 只有主項目或子項目，如 "A" 或 "A1"
      const mainCode = parts[0].charAt(0);
      const main = Object.values(MAIN_CATEGORIES).find(m => m.code === mainCode);
      
      if (!main) return null;
      
      if (parts[0].length === 1) {
        // 只有主項目
        return {
          mainCode: main.code,
          mainName: main.name
        };
      } else {
        // 有子項目
        const subCode = parts[0];
        let subName = '';
        
        if (mainCode === 'A') {
          const sub = Object.values(MARKET_SUB_CATEGORIES).find(s => s.code === subCode);
          subName = sub?.name || '';
        } else if (mainCode === 'E') {
          const sub = Object.values(STRATEGY_SUB_CATEGORIES).find(s => s.code === subCode);
          subName = sub?.name || '';
        }
        
        return {
          mainCode: main.code,
          mainName: main.name,
          subCode,
          subName
        };
      }
    } else if (parts.length === 2) {
      // 完整格式，如 "A1-上市3" 或 "A2-2"
      const subCode = parts[0];
      const windowPart = parts[1];
      const mainCode = subCode.charAt(0);
      
      const main = Object.values(MAIN_CATEGORIES).find(m => m.code === mainCode);
      if (!main) return null;
      
      let subName = '';
      if (mainCode === 'A') {
        const sub = Object.values(MARKET_SUB_CATEGORIES).find(s => s.code === subCode);
        subName = sub?.name || '';
      } else if (mainCode === 'E') {
        const sub = Object.values(STRATEGY_SUB_CATEGORIES).find(s => s.code === subCode);
        subName = sub?.name || '';
      }
      
      // 檢查是否包含台股類股編碼
      let categoryCode = '';
      let categoryName = '';
      let windowIndex = 0;
      
      if (subCode === 'A1') {
        // 台股情況，解析類股編碼
        const categoryMatch = windowPart.match(/^(上市|上櫃|期貨)(\d+)$/);
        if (categoryMatch) {
          categoryCode = categoryMatch[1];
          windowIndex = parseInt(categoryMatch[2]);
          const category = Object.values(TAIWAN_STOCK_CATEGORIES).find(c => c.code === categoryCode);
          categoryName = category?.name || '';
        }
      } else {
        // 美股或其他情況，直接解析數字
        windowIndex = parseInt(windowPart);
      }
      
      const result: IndexConfig = {
        mainCode: main.code,
        mainName: main.name,
        subCode,
        subName,
        windowCode: code,
        windowIndex
      };
      
      if (categoryCode) {
        result.categoryCode = categoryCode;
        result.categoryName = categoryName;
      }
      
      return result;
    }
    
    return null;
  } catch (error) {
    console.error('Failed to parse index code:', error);
    return null;
  }
};

// 獲取所有可用的索引碼
export const getAllAvailableIndexes = () => {
  const indexes: PanelIndexConfig[] = [];
  
  // 台股索引 (包含類股分類)
  Object.values(TAIWAN_STOCK_CATEGORIES).forEach(category => {
    for (let i = 1; i <= 4; i++) {
      const index = generatePanelIndex('MARKET', '台股', category.name, i);
      indexes.push({
        id: `taiwan-${category.code.toLowerCase()}-${i}`,
        index,
        displayCode: generateDisplayCode(index)
      });
    }
  });
  
  // 美股索引
  for (let i = 1; i <= 4; i++) {
    const index = generatePanelIndex('MARKET', '美股', undefined, i);
    indexes.push({
      id: `us-${i}`,
      index,
      displayCode: generateDisplayCode(index)
    });
  }
  
  // 策略開發索引
  Object.values(STRATEGY_SUB_CATEGORIES).forEach(sub => {
    const index = generatePanelIndex('STRATEGY', sub.name);
    indexes.push({
      id: `strategy-${sub.code.toLowerCase()}`,
      index,
      displayCode: generateDisplayCode(index)
    });
  });
  
  return indexes;
};

// 驗證索引碼格式
export const validateIndexCode = (code: string): boolean => {
  const parsed = parseIndexCode(code);
  return parsed !== null;
};

// 根據類股類型獲取對應的索引碼前綴
export const getCategoryPrefix = (categoryType: 'listed' | 'otc' | 'futures'): string => {
  const categoryMap = {
    listed: '上市',
    otc: '上櫃',
    futures: '期貨'
  };
  return categoryMap[categoryType];
};

// 根據視窗編號獲取對應的位置
export const getPositionFromWindowIndex = (windowIndex: number): 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight' => {
  const indexToPositionMap = {
    1: 'topLeft',      // 1 = 左上角
    2: 'bottomLeft',   // 2 = 左下角
    3: 'topRight',     // 3 = 右上角
    4: 'bottomRight'   // 4 = 右下角
  };
  
  return (indexToPositionMap[windowIndex as keyof typeof indexToPositionMap] || 'topLeft') as 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';
};

// 根據位置獲取對應的視窗編號
export const getWindowIndexFromPosition = (position: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight'): number => {
  const positionToIndexMap = {
    topLeft: 1,      // 左上角 = 1
    bottomLeft: 2,   // 左下角 = 2
    topRight: 3,     // 右上角 = 3
    bottomRight: 4   // 右下角 = 4
  };
  
  return positionToIndexMap[position];
};