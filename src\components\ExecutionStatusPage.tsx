import React from 'react';
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

// 模擬交易數據
const tradingData = [
  {
    id: 1,
    inventory: '2330',
    watchlist: '✓',
    alert: '價格突破',
    trading: '進行中',
    direction: '多',
    buyPrice: '567.00',
    sellPrice: '-',
    profit: '+2,340',
    entryTime: '09:15:23',
    exitTime: '-',
    strategyCode: 'PMS001',
    strategyName: '均線突破選股'
  },
  {
    id: 2,
    inventory: '2317',
    watchlist: '✓',
    alert: 'RSI超賣',
    trading: '已完成',
    direction: '空',
    buyPrice: '89.70',
    sellPrice: '87.20',
    profit: '+5,000',
    entryTime: '10:32:15',
    exitTime: '14:28:42',
    strategyCode: 'ALT002',
    strategyName: 'RSI超買超賣警示'
  },
  {
    id: 3,
    inventory: '2454',
    watchlist: '-',
    alert: '成交量異常',
    trading: '進行中',
    direction: '多',
    buyPrice: '734.00',
    sellPrice: '-',
    profit: '-1,200',
    entryTime: '11:45:18',
    exitTime: '-',
    strategyCode: 'TRD001',
    strategyName: '趨勢跟隨策略'
  },
  {
    id: 4,
    inventory: '2881',
    watchlist: '✓',
    alert: '布林通道',
    trading: '等待中',
    direction: '空',
    buyPrice: '67.80',
    sellPrice: '-',
    profit: '0',
    entryTime: '13:22:33',
    exitTime: '-',
    strategyCode: 'PMS003',
    strategyName: '布林通道突破'
  },
  {
    id: 5,
    inventory: '2382',
    watchlist: '✓',
    alert: '動量反轉',
    trading: '已完成',
    direction: '多',
    buyPrice: '245.50',
    sellPrice: '248.90',
    profit: '+2,720',
    entryTime: '14:18:07',
    exitTime: '15:45:12',
    strategyCode: 'TRD002',
    strategyName: '均值回歸策略'
  }
];

const ExecutionStatusPage: React.FC = () => {
  return (
    <div className="h-full bg-gray-900">
      <PanelGroup direction="vertical" className="h-full">
        {/* 上方視窗 */}
        <Panel defaultSize={50} minSize={20}>
          <div className="h-full bg-gray-800 border border-gray-700 rounded-lg m-2 overflow-hidden">
            <div className="bg-gray-700 px-4 py-2 border-b border-gray-600">
              <h2 className="text-white text-lg font-semibold">交易執行狀態表</h2>
              <p className="text-gray-400 text-sm">即時監控所有交易項目的執行狀況</p>
            </div>
            <div className="h-full overflow-auto">
              <Table>
                <TableHeader>
                  <TableRow className="border-gray-600 hover:bg-gray-700/50">
                    <TableHead className="text-gray-300 w-16">項次</TableHead>
                    <TableHead className="text-gray-300 w-20">庫藏股</TableHead>
                    <TableHead className="text-gray-300 w-20">自選股</TableHead>
                    <TableHead className="text-gray-300 w-24">盤中警示</TableHead>
                    <TableHead className="text-gray-300 w-20">交易中</TableHead>
                    <TableHead className="text-gray-300 w-16">多/空</TableHead>
                    <TableHead className="text-gray-300 w-20">買入</TableHead>
                    <TableHead className="text-gray-300 w-20">賣出</TableHead>
                    <TableHead className="text-gray-300 w-20">盈虧</TableHead>
                    <TableHead className="text-gray-300 w-24">進場時間</TableHead>
                    <TableHead className="text-gray-300 w-24">出場時間</TableHead>
                    <TableHead className="text-gray-300 w-24">策略編號</TableHead>
                    <TableHead className="text-gray-300">策略名稱</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tradingData.map((item) => (
                    <TableRow key={item.id} className="border-gray-700 hover:bg-gray-700/30">
                      <TableCell className="text-white font-medium">{item.id}</TableCell>
                      <TableCell className="text-blue-400 font-mono">{item.inventory}</TableCell>
                      <TableCell className="text-center">
                        <span className={`${item.watchlist === '✓' ? 'text-green-400' : 'text-gray-500'}`}>
                          {item.watchlist}
                        </span>
                      </TableCell>
                      <TableCell className="text-yellow-400 text-sm">{item.alert}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded text-xs ${
                          item.trading === '進行中' ? 'bg-green-600 text-white' :
                          item.trading === '已完成' ? 'bg-blue-600 text-white' :
                          'bg-yellow-600 text-white'
                        }`}>
                          {item.trading}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          item.direction === '多' ? 'bg-red-600 text-white' : 'bg-green-600 text-white'
                        }`}>
                          {item.direction}
                        </span>
                      </TableCell>
                      <TableCell className="text-white font-mono text-sm">{item.buyPrice}</TableCell>
                      <TableCell className="text-white font-mono text-sm">{item.sellPrice}</TableCell>
                      <TableCell className={`font-mono text-sm font-medium ${
                        item.profit.startsWith('+') ? 'text-green-400' :
                        item.profit.startsWith('-') ? 'text-red-400' : 'text-gray-400'
                      }`}>
                        {item.profit}
                      </TableCell>
                      <TableCell className="text-gray-300 font-mono text-sm">{item.entryTime}</TableCell>
                      <TableCell className="text-gray-300 font-mono text-sm">{item.exitTime}</TableCell>
                      <TableCell className="text-orange-400 font-mono text-sm">{item.strategyCode}</TableCell>
                      <TableCell className="text-white">{item.strategyName}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </Panel>

        {/* 可拖移的分隔線 */}
        <PanelResizeHandle className="h-2 bg-gray-600 hover:bg-gray-500 transition-colors cursor-row-resize flex items-center justify-center">
          <div className="w-12 h-1 bg-gray-400 rounded-full"></div>
        </PanelResizeHandle>

        {/* 下方視窗 */}
        <Panel defaultSize={50} minSize={20}>
          <div className="h-full bg-gray-800 border border-gray-700 rounded-lg m-2 overflow-hidden">
            <div className="bg-gray-700 px-4 py-2 border-b border-gray-600">
              <h2 className="text-white text-lg font-semibold">下方視窗</h2>
            </div>
            <div className="p-4 h-full overflow-auto">
              <div className="text-gray-300">
                <p>這是下方的可拖移視窗內容區域</p>
                <p className="mt-2 text-gray-400">您可以在這裡放置任何內容</p>
              </div>
            </div>
          </div>
        </Panel>
      </PanelGroup>
    </div>
  );
};

export default ExecutionStatusPage;
