import React from 'react';

interface KLineChartProps {
  selectedStock?: {code: string, name: string} | null;
}

const KLineChart: React.FC<KLineChartProps> = ({ selectedStock }) => {
  return (
    <div className="h-full flex flex-col bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
      {/* 標題欄 */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-800 border-b border-gray-700">
        <h3 className="text-white text-sm font-medium">K線圖視窗</h3>
        {selectedStock && (
          <div className="text-xs text-gray-300">
            {selectedStock.name} ({selectedStock.code})
          </div>
        )}
      </div>

      {/* K線圖內容區域 */}
      <div className="flex-1 p-4">
        {selectedStock ? (
          <div className="h-full flex flex-col items-center justify-center">
            {/* 模擬K線圖 */}
            <div className="w-full h-full bg-gray-800 rounded-lg border border-gray-600 flex flex-col items-center justify-center">
              <div className="text-white text-lg font-medium mb-2">
                {selectedStock.name} ({selectedStock.code})
              </div>
              <div className="text-gray-400 text-sm mb-4">K線圖</div>
              
              {/* 模擬K線圖表 */}
              <div className="w-4/5 h-3/5 bg-gray-900 rounded border border-gray-500 relative overflow-hidden">
                {/* 模擬價格線 */}
                <div className="absolute inset-4">
                  <svg className="w-full h-full" viewBox="0 0 400 200">
                    {/* 網格線 */}
                    <defs>
                      <pattern id="grid" width="40" height="20" patternUnits="userSpaceOnUse">
                        <path d="M 40 0 L 0 0 0 20" fill="none" stroke="#374151" strokeWidth="0.5"/>
                      </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#grid)" />
                    
                    {/* 模擬K線 */}
                    {Array.from({ length: 20 }, (_, i) => {
                      const x = 20 + i * 18;
                      const high = 50 + Math.random() * 100;
                      const low = high - 20 - Math.random() * 30;
                      const open = low + Math.random() * (high - low);
                      const close = low + Math.random() * (high - low);
                      const isRed = close > open;
                      
                      return (
                        <g key={i}>
                          {/* 影線 */}
                          <line 
                            x1={x} y1={high} x2={x} y2={low} 
                            stroke={isRed ? "#ef4444" : "#22c55e"} 
                            strokeWidth="1"
                          />
                          {/* K線實體 */}
                          <rect 
                            x={x - 4} 
                            y={Math.min(open, close)} 
                            width="8" 
                            height={Math.abs(close - open) || 1}
                            fill={isRed ? "#ef4444" : "#22c55e"}
                          />
                        </g>
                      );
                    })}
                    
                    {/* 價格標籤 */}
                    <text x="10" y="30" fill="#9ca3af" fontSize="10">高</text>
                    <text x="10" y="170" fill="#9ca3af" fontSize="10">低</text>
                  </svg>
                </div>
                
                {/* 模擬價格信息 */}
                <div className="absolute top-2 left-2 text-xs text-gray-400">
                  <div>開: {(Math.random() * 100 + 50).toFixed(2)}</div>
                  <div>高: {(Math.random() * 100 + 80).toFixed(2)}</div>
                  <div>低: {(Math.random() * 100 + 30).toFixed(2)}</div>
                  <div>收: {(Math.random() * 100 + 60).toFixed(2)}</div>
                </div>
              </div>
              
              {/* 模擬控制按鈕 */}
              <div className="mt-4 flex space-x-2">
                <button className="px-3 py-1 bg-gray-700 text-white text-xs rounded hover:bg-gray-600">
                  日線
                </button>
                <button className="px-3 py-1 bg-gray-700 text-white text-xs rounded hover:bg-gray-600">
                  週線
                </button>
                <button className="px-3 py-1 bg-gray-700 text-white text-xs rounded hover:bg-gray-600">
                  月線
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-gray-500">
              <div className="text-lg mb-2">📈</div>
              <div className="text-sm">請從左側類股清單中選擇個股</div>
              <div className="text-xs text-gray-600 mt-1">選擇個股後將顯示K線圖</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default KLineChart;
