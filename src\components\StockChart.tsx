import React, { useState, useRef, useCallback, useMemo } from 'react';
import { Settings } from 'lucide-react';
import ColumnSelector from './ColumnSelector';

interface StockChartProps {
  onCategorySelect?: (categoryCode: string, categoryName: string) => void;
}

interface ColumnConfig {
  key: string;
  label: string;
  visible: boolean;
  required?: boolean;
}

const StockChart: React.FC<StockChartProps> = ({ onCategorySelect }) => {
  // 上市19類股數據 - 使用 useMemo 避免重複創建
  const listedCategories = useMemo(() => [
    { code: 'water', name: '水泥', price: '137.41c', change: '▲0.92', changePercent: '+0.67', volume: '8.66', volumePercent: '0.2230' },
    { code: 'food', name: '食品', price: '2080.68c', change: '▲26.17', changePercent: '+1.27', volume: '44.20', volumePercent: '1.1390' },
    { code: 'plastic', name: '塑膠', price: '98.96c', change: '▲1.77', changePercent: '+1.82', volume: '29.22', volumePercent: '0.7529' },
    { code: 'textile', name: '紡織纖維', price: '527.76c', change: '▲5.41', changePercent: '+1.04', volume: '11.83', volumePercent: '0.3049' },
    { code: 'machinery', name: '電機機械', price: '375.75c', change: '▲2.73', changePercent: '+0.73', volume: '198.36', volumePercent: '5.1115' },
    { code: 'cable', name: '電器電纜', price: '84.50c', change: '▲0.71', changePercent: '+0.85', volume: '15.22', volumePercent: '0.3920' },
    { code: 'chemical', name: '化學生技醫療', price: '134.65c', change: '▲1.41', changePercent: '+1.06', volume: '45.13', volumePercent: '1.1630' },
    { code: 'glass', name: '玻璃陶瓷', price: '45.29c', change: '▲0.36', changePercent: '+0.80', volume: '1.67', volumePercent: '0.0429' },
    { code: 'paper', name: '造紙', price: '240.69c', change: '▲2.71', changePercent: '+1.14', volume: '1.45', volumePercent: '0.0374' },
    { code: 'steel', name: '鋼鐵', price: '117.53c', change: '▲1.27', changePercent: '+1.09', volume: '25.82', volumePercent: '0.6654' },
    { code: 'rubber', name: '橡膠', price: '221.31c', change: '▲4.39', changePercent: '+2.02', volume: '5.60', volumePercent: '0.1444' },
    { code: 'auto', name: '汽車', price: '330.96c', change: '▲5.34', changePercent: '+1.64', volume: '30.24', volumePercent: '0.7792' },
    { code: 'electronics', name: '電子', price: '1253.88c', change: '▲0.98', changePercent: '+0.08', volume: '2714.08', volumePercent: '69.9383' },
    { code: 'construction', name: '建材營造', price: '507.25c', change: '▲7.62', changePercent: '+1.53', volume: '23.14', volumePercent: '0.5962' },
    { code: 'shipping', name: '航運業', price: '193.16c', change: '▼0.27', changePercent: '-0.14', volume: '98.25', volumePercent: '2.5318' },
    { code: 'tourism', name: '觀光餐旅', price: '113.96c', change: '▲1.54', changePercent: '+1.37', volume: '4.93', volumePercent: '0.1269' },
    { code: 'finance', name: '金融保險', price: '2157.97c', change: '▲10.47', changePercent: '+0.49', volume: '228.56', volumePercent: '5.8895' },
    { code: 'retail', name: '貿易百貨', price: '258.72c', change: '▲1.05', changePercent: '+0.41', volume: '6.56', volumePercent: '0.1690' },
    { code: 'others', name: '其他', price: '292.50c', change: '▲1.97', changePercent: '+0.68', volume: '25.58', volumePercent: '0.6592' },
  ], []);

  // 選中的商品狀態
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // 欄位配置狀態
  const [columns, setColumns] = useState<ColumnConfig[]>([
    { key: 'product', label: '商品', visible: true, required: true },
    { key: 'price', label: '成交', visible: true },
    { key: 'change', label: '漲跌', visible: true },
    { key: 'changePercent', label: '漲跌%', visible: true },
    { key: 'volume', label: '成交值', visible: true },
    { key: 'volumePercent', label: '成交比重%', visible: true },
  ]);

  // 欄位寬度狀態 (百分比) - 確保總和為100%
  const [columnWidths, setColumnWidths] = useState({
    product: 25,       // 商品
    price: 15,         // 成交
    change: 12,        // 漲跌
    changePercent: 12, // 漲跌%
    volume: 18,        // 成交值
    volumePercent: 18  // 成交比重%
  });

  const [isDragging, setIsDragging] = useState<string | null>(null);
  const [showColumnSelector, setShowColumnSelector] = useState(false);
  const tableRef = useRef<HTMLTableElement>(null);

  // 獲取可見欄位 - 使用 useMemo 優化
  const visibleColumns = useMemo(() => columns.filter(col => col.visible), [columns]);

  // 重新計算欄位寬度以適應可見欄位
  const calculateColumnWidths = useCallback(() => {
    const visibleCount = visibleColumns.length;
    if (visibleCount === 0) return columnWidths;

    const baseWidth = 100 / visibleCount;
    const newWidths = { ...columnWidths };
    
    // 重置所有可見欄位的寬度
    visibleColumns.forEach(col => {
      newWidths[col.key as keyof typeof columnWidths] = baseWidth;
    });

    return newWidths;
  }, [visibleColumns, columnWidths]);

  // 使用 useCallback 優化點擊處理函數
  const handleCategoryClick = useCallback((category: typeof listedCategories[0]) => {
    // 設置選中的商品
    setSelectedCategory(category.code);
    
    if (onCategorySelect) {
      onCategorySelect(category.code, category.name);
    }
  }, [onCategorySelect]);

  // 處理欄位配置變更
  const handleColumnsChange = useCallback((newColumns: ColumnConfig[]) => {
    setColumns(newColumns);
    // 重新計算寬度
    const newWidths = calculateColumnWidths();
    setColumnWidths(newWidths);
  }, [calculateColumnWidths]);

  // 處理欄位調整開始
  const handleResizeStart = useCallback((columnKey: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(columnKey);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, []);

  // 處理欄位調整
  const handleResize = useCallback((e: MouseEvent) => {
    if (!isDragging || !tableRef.current) return;

    const tableRect = tableRef.current.getBoundingClientRect();
    const mouseX = e.clientX - tableRect.left;
    const tableWidth = tableRect.width;
    const mousePercent = (mouseX / tableWidth) * 100;

    setColumnWidths(prev => {
      const newWidths = { ...prev };
      const visibleColumnKeys = visibleColumns.map(col => col.key);
      const currentIndex = visibleColumnKeys.indexOf(isDragging);
      
      if (currentIndex === -1) return prev;

      // 計算當前欄位之前所有欄位的總寬度
      let totalPrevWidth = 0;
      for (let i = 0; i < currentIndex; i++) {
        totalPrevWidth += newWidths[visibleColumnKeys[i] as keyof typeof newWidths];
      }

      // 計算新的欄位寬度
      const newWidth = Math.max(8, Math.min(40, mousePercent - totalPrevWidth));
      const oldWidth = newWidths[isDragging as keyof typeof newWidths];
      const widthDiff = newWidth - oldWidth;

      // 更新當前欄位寬度
      newWidths[isDragging as keyof typeof newWidths] = newWidth;

      // 調整後續欄位寬度以保持總寬度為100%
      if (currentIndex < visibleColumnKeys.length - 1) {
        const remainingColumns = visibleColumnKeys.slice(currentIndex + 1);
        const totalRemainingWidth = remainingColumns.reduce((sum, col) => 
          sum + newWidths[col as keyof typeof newWidths], 0
        );

        if (totalRemainingWidth > 0) {
          remainingColumns.forEach(col => {
            const currentColWidth = newWidths[col as keyof typeof newWidths];
            const ratio = currentColWidth / totalRemainingWidth;
            newWidths[col as keyof typeof newWidths] = Math.max(8, currentColWidth - (widthDiff * ratio));
          });
        }
      }

      return newWidths;
    });
  }, [isDragging, visibleColumns]);

  // 處理欄位調整結束
  const handleResizeEnd = useCallback(() => {
    setIsDragging(null);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, []);

  // 監聽滑鼠事件
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleResize);
      document.addEventListener('mouseup', handleResizeEnd);
      
      return () => {
        document.removeEventListener('mousemove', handleResize);
        document.removeEventListener('mouseup', handleResizeEnd);
      };
    }
  }, [isDragging, handleResize, handleResizeEnd]);

  // 渲染表格標題 - 使用 useMemo 優化
  const renderTableHeader = useMemo(() => {
    return (
      <tr className="text-gray-300">
        {visibleColumns.map((column, index) => (
          <th 
            key={column.key}
            className={`px-2 py-1 border-r border-gray-700 relative group ${
              column.key === 'product' ? 'text-left' : 'text-right'
            }`}
            style={{ width: `${columnWidths[column.key as keyof typeof columnWidths]}%` }}
          >
            {column.label}
            {/* 只有非最後一個欄位才顯示調整控制項 */}
            {index < visibleColumns.length - 1 && (
              <div
                className="absolute top-0 right-0 w-1 h-full cursor-col-resize bg-transparent hover:bg-blue-500/50 transition-colors z-10"
                onMouseDown={(e) => handleResizeStart(column.key, e)}
              />
            )}
          </th>
        ))}
      </tr>
    );
  }, [visibleColumns, columnWidths, handleResizeStart]);

  // 渲染表格行 - 使用 React.memo 優化
  const TableRow = React.memo(({ category, index }: { category: typeof listedCategories[0], index: number }) => {
    const cellData = {
      product: category.name,
      price: category.price,
      change: category.change,
      changePercent: category.changePercent,
      volume: category.volume,
      volumePercent: category.volumePercent
    };

    const isSelected = selectedCategory === category.code;

    return (
      <tr 
        className="hover:bg-gray-800 border-b border-gray-800 cursor-pointer transition-colors duration-200"
        onClick={() => handleCategoryClick(category)}
      >
        {visibleColumns.map((column) => {
          const cellValue = cellData[column.key as keyof typeof cellData];
          const isProduct = column.key === 'product';
          const isPrice = column.key === 'price';
          const isChange = column.key === 'change';
          const isChangePercent = column.key === 'changePercent';
          
          let cellClass = `px-2 py-1 border-r border-gray-700 truncate ${
            isProduct ? 'text-white font-medium hover:text-blue-400 text-left' : 'text-right'
          }`;

          // 根據漲跌設定顏色
          if (isPrice || isChange) {
            if (category.change.includes('▲')) {
              cellClass += ' text-red-400 font-medium';
            } else if (category.change.includes('▼')) {
              cellClass += ' text-green-400';
            } else {
              cellClass += ' text-white';
            }
          } else if (isChangePercent) {
            if (category.changePercent.includes('+')) {
              cellClass += ' text-red-400';
            } else if (category.changePercent.includes('-')) {
              cellClass += ' text-green-400';
            } else {
              cellClass += ' text-white';
            }
          } else if (!isProduct) {
            cellClass += ' text-white';
          }

          return (
            <td 
              key={column.key}
              className={cellClass}
              style={{ width: `${columnWidths[column.key as keyof typeof columnWidths]}%` }}
            >
              {/* 在商品欄位顯示選中指示符 - 使用兩個實心向右三角形 ▶▶ */}
              {isProduct ? (
                <span>
                  {isSelected && <span className="text-blue-400 mr-1">▶▶</span>}
                  {cellValue}
                </span>
              ) : (
                cellValue
              )}
            </td>
          );
        })}
      </tr>
    );
  });

  return (
    <div className="stock-chart-container bg-gray-900 rounded border border-gray-700 p-2 relative">
      {/* Chart Header */}
      <div className="flex items-center justify-between mb-0.5 h-8">
        <div className="flex items-center space-x-4">
          {/* 欄位設定按鈕 */}
          <button
            onClick={() => setShowColumnSelector(true)}
            className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-800 rounded transition-colors"
          >
            <Settings className="w-4 h-4" />
          </button>
          <span className="text-white font-bold">上市19類</span>
          <span className="text-white">13:35:00</span>
        </div>
      </div>

      {/* 上市19類股表格 - 使用統一的標準捲軸樣式，確保完全填滿容器 */}
      <div className="table-container custom-scrollbar">
        <table ref={tableRef} className="w-full text-xs table-fixed border-collapse">
          <thead className="bg-gray-800 sticky top-0">
            {renderTableHeader}
          </thead>
          <tbody>
            {listedCategories.map((category, index) => (
              <TableRow key={category.code} category={category} index={index} />
            ))}
          </tbody>
        </table>
      </div>

      {/* 欄位選擇器 */}
      <ColumnSelector
        isOpen={showColumnSelector}
        onClose={() => setShowColumnSelector(false)}
        columns={columns}
        onColumnsChange={handleColumnsChange}
        title="欄位設定(A1-上市1-1)"
      />

      {/* 拖拽時的覆蓋層 */}
      {isDragging && (
        <div className="absolute inset-0 z-30 bg-transparent" />
      )}
    </div>
  );
};

export default StockChart;