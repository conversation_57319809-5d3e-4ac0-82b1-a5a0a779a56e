import React from 'react';
import { 
  Search, 
  Plus, 
  Bell, 
  ChevronDown,
  Grid3X3,
  List,
  Calendar,
  Filter,
  SortDesc,
  Share2,
  Download,
  Sparkles
} from 'lucide-react';

const TopNavigation = () => {
  return (
    <div className="bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl border-b border-white/20 dark:border-gray-700/50 px-8 py-6 sticky top-0 z-20">
      <div className="flex items-center justify-between">
        {/* Left Section */}
        <div className="flex items-center space-x-6">
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
              Tasks Overview
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Manage your team's work and track progress across projects
            </p>
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
            <input
              type="text"
              placeholder="Search tasks, projects..."
              className="pl-12 pr-4 py-3 w-80 text-sm bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-600/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 shadow-sm"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <button className="p-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/70 dark:hover:bg-gray-800/50 rounded-xl transition-all duration-200">
              <Filter className="h-4 w-4" />
            </button>
            <button className="p-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/70 dark:hover:bg-gray-800/50 rounded-xl transition-all duration-200">
              <SortDesc className="h-4 w-4" />
            </button>
            <button className="p-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/70 dark:hover:bg-gray-800/50 rounded-xl transition-all duration-200">
              <Share2 className="h-4 w-4" />
            </button>
          </div>

          {/* View Toggle */}
          <div className="flex items-center bg-gray-100/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-1 shadow-sm">
            <button className="p-2.5 text-white bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-sm transition-all duration-200">
              <Grid3X3 className="h-4 w-4" />
            </button>
            <button className="p-2.5 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/70 dark:hover:bg-gray-700/50 rounded-lg transition-all duration-200">
              <List className="h-4 w-4" />
            </button>
            <button className="p-2.5 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/70 dark:hover:bg-gray-700/50 rounded-lg transition-all duration-200">
              <Calendar className="h-4 w-4" />
            </button>
          </div>

          {/* New Task Button */}
          <button className="flex items-center space-x-2 px-6 py-3 text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 hover:transform hover:scale-105">
            <Plus className="h-4 w-4" />
            <span>New Task</span>
          </button>

          {/* Notifications */}
          <button className="relative p-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/70 dark:hover:bg-gray-800/50 rounded-xl transition-all duration-200">
            <Bell className="h-5 w-5" />
            <span className="absolute -top-1 -right-1 h-3 w-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-full shadow-sm"></span>
          </button>

          {/* Profile */}
          <div className="flex items-center space-x-3 pl-4 border-l border-gray-200/50 dark:border-gray-700/50">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white text-sm font-bold">JD</span>
            </div>
            <div className="hidden md:block">
              <div className="text-sm font-medium text-gray-900 dark:text-white">John Doe</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Product Manager</div>
            </div>
            <button className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
              <ChevronDown className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopNavigation;