import React, { useState } from 'react';
import { 
  LayoutDashboard, 
  CheckSquare, 
  Target, 
  FileText, 
  Calendar, 
  Zap, 
  Settings,
  Plus,
  ChevronRight,
  Sparkles,
  Users,
  BarChart3,
  Clock
} from 'lucide-react';

const Sidebar = () => {
  const [activeItem, setActiveItem] = useState('Tasks');

  const navigationItems = [
    { icon: LayoutDashboard, label: 'Dashboard', count: null },
    { icon: CheckSquare, label: 'Tasks', count: 24 },
    { icon: Target, label: 'Goals', count: 8 },
    { icon: Users, label: 'Team', count: null },
    { icon: BarChart3, label: 'Analytics', count: null },
    { icon: Calendar, label: 'Calendar', count: 3 },
    { icon: FileText, label: 'Docs', count: 12 },
    { icon: Zap, label: 'Automations', count: null },
  ];

  const workspaceItems = [
    { emoji: '🎯', label: 'Product Launch', count: 24, color: 'from-purple-500 to-pink-500' },
    { emoji: '🚀', label: 'Marketing Campaign', count: 18, color: 'from-blue-500 to-cyan-500' },
    { emoji: '💡', label: 'Innovation Lab', count: 12, color: 'from-green-500 to-emerald-500' },
    { emoji: '📊', label: 'Data Analytics', count: 8, color: 'from-orange-500 to-red-500' },
  ];

  return (
    <div className="w-72 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl h-screen flex flex-col border-r border-white/20 dark:border-gray-700/50 shadow-xl">
      {/* Header */}
      <div className="p-6 border-b border-gray-100/50 dark:border-gray-700/50">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
              ProjectFlow
            </h1>
            <p className="text-xs text-gray-500 dark:text-gray-400">Workspace Pro</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 py-6 overflow-y-auto">
        <nav className="space-y-2 px-4">
          {navigationItems.map((item, index) => (
            <button
              key={index}
              onClick={() => setActiveItem(item.label)}
              className={`group w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ${
                activeItem === item.label
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg shadow-blue-500/25 transform scale-[1.02]'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100/70 dark:hover:bg-gray-800/50 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <div className="flex items-center">
                <item.icon className={`mr-3 h-5 w-5 ${
                  activeItem === item.label ? 'text-white' : 'text-gray-500 dark:text-gray-400'
                }`} />
                {item.label}
              </div>
              {item.count && (
                <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                  activeItem === item.label 
                    ? 'bg-white/20 text-white' 
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                }`}>
                  {item.count}
                </span>
              )}
            </button>
          ))}
        </nav>

        {/* Workspace Section */}
        <div className="mt-8 px-4">
          <div className="flex items-center justify-between px-4 py-3">
            <h3 className="text-xs font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Workspaces
            </h3>
            <button className="p-1.5 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
              <Plus className="h-4 w-4" />
            </button>
          </div>
          <div className="space-y-2 mt-3">
            {workspaceItems.map((item, index) => (
              <button
                key={index}
                className="group w-full flex items-center justify-between px-4 py-3 text-sm text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-100/70 dark:hover:bg-gray-800/50 hover:text-gray-900 dark:hover:text-white transition-all duration-200 hover:transform hover:scale-[1.01]"
              >
                <div className="flex items-center">
                  <div className={`w-8 h-8 bg-gradient-to-r ${item.color} rounded-lg flex items-center justify-center mr-3 shadow-sm`}>
                    <span className="text-sm">{item.emoji}</span>
                  </div>
                  <div className="text-left">
                    <div className="font-medium">{item.label}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{item.count} tasks</div>
                  </div>
                </div>
                <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" />
              </button>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 px-4">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-4 border border-blue-100 dark:border-blue-800/30">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <span className="text-sm font-medium text-blue-900 dark:text-blue-100">Quick Stats</span>
            </div>
            <div className="space-y-2 text-xs">
              <div className="flex justify-between text-gray-600 dark:text-gray-300">
                <span>Due Today</span>
                <span className="font-medium">5 tasks</span>
              </div>
              <div className="flex justify-between text-gray-600 dark:text-gray-300">
                <span>In Progress</span>
                <span className="font-medium">12 tasks</span>
              </div>
              <div className="flex justify-between text-gray-600 dark:text-gray-300">
                <span>Completed</span>
                <span className="font-medium">28 tasks</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Settings */}
      <div className="p-4 border-t border-gray-100/50 dark:border-gray-700/50">
        <button className="w-full flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-100/70 dark:hover:bg-gray-800/50 hover:text-gray-900 dark:hover:text-white transition-all duration-200">
          <Settings className="mr-3 h-5 w-5 text-gray-500 dark:text-gray-400" />
          Settings
        </button>
      </div>
    </div>
  );
};

export default Sidebar;