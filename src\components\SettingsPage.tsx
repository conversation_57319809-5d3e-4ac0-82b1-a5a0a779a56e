import React, { useState } from 'react';
import { Settings, User, Bell, Shield, Database, Palette, Globe, HelpCircle } from 'lucide-react';

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('general');

  const tabs = [
    { id: 'general', label: '一般設定', icon: Settings },
    { id: 'account', label: '帳戶設定', icon: User },
    { id: 'notifications', label: '通知設定', icon: Bell },
    { id: 'security', label: '安全設定', icon: Shield },
    { id: 'data', label: '資料設定', icon: Database },
    { id: 'appearance', label: '外觀設定', icon: Palette },
    { id: 'language', label: '語言設定', icon: Globe },
    { id: 'help', label: '說明與支援', icon: HelpCircle }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white mb-4">一般設定</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-white text-sm font-medium">自動儲存</label>
                  <p className="text-gray-400 text-xs">自動儲存您的工作進度</p>
                </div>
                <input type="checkbox" className="toggle" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-white text-sm font-medium">啟動時開啟上次工作區</label>
                  <p className="text-gray-400 text-xs">重新開啟上次關閉的頁面</p>
                </div>
                <input type="checkbox" className="toggle" />
              </div>
            </div>
          </div>
        );
      case 'account':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white mb-4">帳戶設定</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-white text-sm font-medium mb-2">使用者名稱</label>
                <input 
                  type="text" 
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white text-sm"
                  placeholder="輸入使用者名稱"
                />
              </div>
              <div>
                <label className="block text-white text-sm font-medium mb-2">電子郵件</label>
                <input 
                  type="email" 
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white text-sm"
                  placeholder="輸入電子郵件"
                />
              </div>
            </div>
          </div>
        );
      case 'notifications':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white mb-4">通知設定</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-white text-sm font-medium">交易通知</label>
                  <p className="text-gray-400 text-xs">接收交易相關通知</p>
                </div>
                <input type="checkbox" className="toggle" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-white text-sm font-medium">價格警示</label>
                  <p className="text-gray-400 text-xs">股價達到設定條件時通知</p>
                </div>
                <input type="checkbox" className="toggle" defaultChecked />
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white mb-4">{tabs.find(tab => tab.id === activeTab)?.label}</h3>
            <p className="text-gray-400">此功能正在開發中...</p>
          </div>
        );
    }
  };

  return (
    <div className="h-full bg-gray-900 flex">
      {/* 左側選單 */}
      <div className="w-64 bg-gray-800 border-r border-gray-700">
        <div className="p-4 border-b border-gray-700">
          <h2 className="text-white text-lg font-semibold flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            基本設定
          </h2>
        </div>
        <nav className="p-2">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center px-3 py-2 rounded text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <Icon className="w-4 h-4 mr-3" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* 右側內容 */}
      <div className="flex-1 p-6">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default SettingsPage;
