'use client';

import React, { useState } from 'react';
import BlankHomePage from '@/components/BlankHomePage';
import TaiwanStockPageWithSplitPane from '@/components/TaiwanStockPageWithSplitPane';

import StrategyDevelopmentPage from '@/components/StrategyDevelopmentPage';
import SettingsPage from '@/components/SettingsPage';
import TopToolbar from '@/components/TopToolbar';
import { PanelProvider } from '@/contexts/PanelContext';

export default function App() {
  const [currentMarket, setCurrentMarket] = useState<'taiwan' | 'us'>('taiwan');
  const [currentPage, setCurrentPage] = useState<'home' | 'market' | 'watchlist' | 'strategy-development' | 'settings'>('market');
  const [showStrategyPanel, setShowStrategyPanel] = useState<boolean>(false);
  const [strategyCategory, setStrategyCategory] = useState<string>('strategySettings');

  const handleMarketChange = (market: 'taiwan' | 'us') => {
    setCurrentMarket(market);
  };

  const handlePageChange = (page: 'home' | 'market' | 'watchlist' | 'strategy-development' | 'settings') => {
    console.log('Page change requested:', page);
    setCurrentPage(page);
    // 當切換到其他頁面時，關閉策略面板
    if (page !== 'market') {
      setShowStrategyPanel(false);
    }
  };

  const handleToggleStrategyPanel = (category: string) => {
    setStrategyCategory(category);
    setShowStrategyPanel(true);
    // 確保在市場頁面
    if (currentPage !== 'market') {
      setCurrentPage('market');
    }
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'home':
        return <BlankHomePage />;
      case 'market':
        return (
          <TaiwanStockPageWithSplitPane
            showStrategyPanel={showStrategyPanel}
            strategyCategory={strategyCategory}
            onCloseStrategyPanel={() => setShowStrategyPanel(false)}
          />
        );

      case 'strategy-development':
        return <StrategyDevelopmentPage />;
      case 'settings':
        return <SettingsPage />;
      default:
        return (
          <TaiwanStockPageWithSplitPane
            showStrategyPanel={showStrategyPanel}
            strategyCategory={strategyCategory}
            onCloseStrategyPanel={() => setShowStrategyPanel(false)}
          />
        );
    }
  };

  return (
    <div className="h-screen bg-gray-900">
      {/* 固定頂部區域 */}
      <TopToolbar
        currentMarket={currentMarket}
        onMarketChange={handleMarketChange}
        currentPage={currentPage}
        onPageChange={handlePageChange}
        showStrategyPanel={showStrategyPanel}
        onToggleStrategyPanel={handleToggleStrategyPanel}
      />
      {/* 固定分割線 */}
      <div className="fixed top-[72px] left-0 right-0 z-40 h-1 bg-gradient-to-r from-gray-700 via-gray-600 to-gray-700 shadow-inner"></div>

      {/* 主要內容區域 - 為固定頂部留出空間 */}
      <div className="pt-[76px] h-screen">
        <PanelProvider market={currentMarket}>
          <div className="h-full overflow-auto">
            {renderCurrentPage()}
          </div>
        </PanelProvider>
      </div>
    </div>
  );
}
