import React, { useMemo } from 'react';

const MarketData = () => {
  // 使用 useMemo 避免重複創建數據
  const marketData = useMemo(() => [
    { code: '▶▶3347', name: '世界', price: '97.5c', change: '▲2.00', changePercent: '+2.08', volume: '587', totalVolume: '7670', time: '13:30:00' },
    { code: '5274', name: '信驊', price: '3001c', change: '▲70.00', changePercent: '+2.39', volume: '66', totalVolume: '736', time: '13:30:00' },
    { code: '3529', name: '力旺', price: '2430c', change: '▲70.00', changePercent: '+2.97', volume: '66', totalVolume: '591', time: '13:30:00' },
    { code: '6770', name: '力積電', price: '103.0', change: '▲4.50', changePercent: '+4.57', volume: '1621', totalVolume: '1621', time: '13:30:00' },
    { code: '8299', name: '群聯', price: '521c', change: '▲3.00', changePercent: '+0.58', volume: '157', totalVolume: '1267', time: '13:30:00' },
    { code: '6279', name: '胡連', price: '925c', change: '0.00', changePercent: '0.00', volume: '173', totalVolume: '1944', time: '13:30:00' },
  ], []);

  const timeData = useMemo(() => [
    { time: '13:12:00', buy: '2.35', sell: '5.80', volume: '0.49', buyVol: '1.02', sellVol: '0.90', netVol: '0.19', buyCount: '2.21', sellCount: '6.44' },
    { time: '13:13:00', buy: '7.35', sell: '5.80', volume: '0.51', buyVol: '1.02', sellVol: '0.90', netVol: '0.19', buyCount: '7.21', sellCount: '6.44' },
    { time: '13:14:00', buy: '0.72', sell: '-0.20', volume: '0.34', buyVol: '1.00', sellVol: '0.90', netVol: '0.18', buyCount: '0.71', sellCount: '-0.84' },
  ], []);

  // 使用 React.memo 優化表格行組件
  const StockRow = React.memo(({ stock, index }: { stock: typeof marketData[0], index: number }) => (
    <tr key={index} className="hover:bg-gray-800 border-b border-gray-800">
      <td className="px-1 py-1 text-white text-xs">{stock.code}</td>
      <td className="px-1 py-1 text-white text-xs">{stock.name}</td>
      <td className={`px-1 py-1 text-right text-xs ${
        stock.change.includes('▲') ? 'text-red-400' : 
        stock.change.includes('▼') ? 'text-green-400' : 'text-white'
      }`}>{stock.price}</td>
      <td className={`px-1 py-1 text-right text-xs ${
        stock.change.includes('▲') ? 'text-red-400' : 
        stock.change.includes('▼') ? 'text-green-400' : 'text-white'
      }`}>{stock.change}</td>
      <td className={`px-1 py-1 text-right text-xs ${
        stock.changePercent.includes('+') ? 'text-red-400' : 
        stock.changePercent.includes('-') ? 'text-green-400' : 'text-white'
      }`}>{stock.changePercent}</td>
      <td className="px-1 py-1 text-white text-right text-xs">{stock.volume}</td>
      <td className="px-1 py-1 text-white text-right text-xs">{stock.totalVolume}</td>
      <td className="px-1 py-1 text-gray-300 text-center text-xs">{stock.time}</td>
    </tr>
  ));

  const TimeDataRow = React.memo(({ data, index }: { data: typeof timeData[0], index: number }) => (
    <tr key={index} className="hover:bg-gray-800 border-b border-gray-800">
      <td className="px-1 py-1 text-gray-300 text-center">{data.time}</td>
      <td className="px-1 py-1 text-red-400 text-right">{data.buy}</td>
      <td className="px-1 py-1 text-green-400 text-right">{data.sell}</td>
      <td className="px-1 py-1 text-white text-right">{data.volume}</td>
      <td className="px-1 py-1 text-white text-right">{data.buyVol}</td>
      <td className="px-1 py-1 text-white text-right">{data.sellVol}</td>
      <td className="px-1 py-1 text-white text-right">{data.netVol}</td>
      <td className="px-1 py-1 text-white text-right">{data.buyCount}</td>
      <td className="px-1 py-1 text-white text-right">{data.sellCount}</td>
    </tr>
  ));

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* Top Chart Area */}
      <div className="h-64 p-2 border-b border-gray-700">
        <div className="bg-black rounded border border-gray-700 h-full p-2">
          <div className="flex items-center justify-between mb-2">
            <span className="text-white text-sm font-bold">中華指數(OTC62)</span>
            <span className="text-white text-sm">13:35:00</span>
            <span className="text-red-400 font-bold">106.82c</span>
            <span className="text-red-400">▲1.42 +1.35%</span>
            <span className="text-gray-400">397.86億</span>
          </div>
          
          {/* Mini Chart */}
          <div className="h-32 relative">
            <svg className="w-full h-full">
              <polyline
                fill="none"
                stroke="#EF4444"
                strokeWidth="1"
                points="0,80 50,70 100,60 150,65 200,55 250,50 300,45"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Stock List */}
      <div className="flex-1 overflow-auto custom-scrollbar table-container">
        <table className="w-full text-xs">
          <thead className="bg-gray-800 sticky top-0">
            <tr className="text-gray-300">
              <th className="px-1 py-1 text-left">代碼</th>
              <th className="px-1 py-1 text-left">簡稱</th>
              <th className="px-1 py-1 text-right">成交</th>
              <th className="px-1 py-1 text-right">漲跌</th>
              <th className="px-1 py-1 text-right">%</th>
              <th className="px-1 py-1 text-right">量</th>
              <th className="px-1 py-1 text-right">總量</th>
              <th className="px-1 py-1 text-center">時間</th>
            </tr>
          </thead>
          <tbody>
            {marketData.map((stock, index) => (
              <StockRow key={stock.code} stock={stock} index={index} />
            ))}
          </tbody>
        </table>
      </div>

      {/* Bottom Time Data */}
      <div className="h-32 border-t border-gray-700 overflow-auto custom-scrollbar table-container">
        <table className="w-full text-xs">
          <thead className="bg-gray-800 sticky top-0">
            <tr className="text-gray-300">
              <th className="px-1 py-1 text-center">時間</th>
              <th className="px-1 py-1 text-right">委買</th>
              <th className="px-1 py-1 text-right">委賣</th>
              <th className="px-1 py-1 text-right">成交量</th>
              <th className="px-1 py-1 text-right">委買量</th>
              <th className="px-1 py-1 text-right">委賣量</th>
              <th className="px-1 py-1 text-right">成交筆</th>
              <th className="px-1 py-1 text-right">委買筆</th>
              <th className="px-1 py-1 text-right">委賣筆</th>
            </tr>
          </thead>
          <tbody>
            {timeData.map((data, index) => (
              <TimeDataRow key={data.time} data={data} index={index} />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default MarketData;